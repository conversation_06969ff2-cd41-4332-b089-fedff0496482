"""
Data models for post generation functionality.
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field


class PostGenerationRequest(BaseModel):
    """Request schema for post generation."""
    # Brand information from steps 1-3
    brandInfo: Dict[str, Any] = Field(..., description="Complete brand information")
    
    # Design configuration from step 4
    designConfig: Dict[str, Any] = Field(..., description="Design and template configuration")
    
    # Generation configuration
    generationConfig: Dict[str, Any] = Field(default_factory=dict, description="Generation settings")


class GeneratedPost(BaseModel):
    """Schema for a single generated post."""
    id: str = Field(..., description="Unique post ID")
    text: str = Field(..., description="Generated text content")
    description: Optional[str] = Field(None, description="Post description/caption for social media")
    image_url: Optional[str] = Field(None, description="Generated clean background image URL")
    template: str = Field(..., description="Template used")
    platform: str = Field(..., description="Target platform")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    polotno_json: Optional[Dict[str, Any]] = Field(None, description="Polotno editor JSON structure with editable layers")


class PostGenerationResponse(BaseModel):
    """Response schema for post generation."""
    success: bool = Field(..., description="Whether generation was successful")
    posts: List[GeneratedPost] = Field(default_factory=list, description="Generated posts")
    total_generated: int = Field(0, description="Total number of posts generated")
    error: Optional[str] = Field(None, description="Error message if generation failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Generation metadata")


class MemeGenerationRequest(BaseModel):
    """Request schema for meme generation test."""
    prompt: str = Field(..., description="Meme text/prompt")
    business_name: str = Field(default="Business", description="Name of the business")
    industry: str = Field(default="general", description="Industry type")


class ContentStrategy(BaseModel):
    """Schema for content strategy analysis."""
    topic: str = Field(..., description="Main topic")
    context_analysis: Dict[str, Any] = Field(default_factory=dict, description="AI context analysis")
    template_vibe: str = Field(..., description="Template vibe/mood")
    platform_specs: Dict[str, Any] = Field(default_factory=dict, description="Platform specifications")
    brand_context: Dict[str, Any] = Field(default_factory=dict, description="Brand context")


class ExtractedInfo(BaseModel):
    """Schema for extracted information from natural descriptions."""
    tema_principal: str = Field(..., description="Main topic extracted")
    nombre_marca: str = Field(..., description="Brand name extracted")
    industria: str = Field(..., description="Industry identified")
    productos_servicios: List[str] = Field(default_factory=list, description="Products/services")
    audiencia_objetivo: str = Field(..., description="Target audience")
    palabras_clave: List[str] = Field(default_factory=list, description="Keywords")


class ContextAnalysis(BaseModel):
    """Schema for topic context analysis."""
    nicho: str = Field(..., description="Content niche")
    audiencia: str = Field(..., description="Target audience")
    estilo_visual: str = Field(..., description="Visual style")
    tono: str = Field(..., description="Communication tone")
    formatos_populares: List[str] = Field(default_factory=list, description="Popular formats")
    palabras_clave: List[str] = Field(default_factory=list, description="Keywords")
    tipo_contenido: str = Field(..., description="Content type")
    nivel_formalidad: str = Field(..., description="Formality level")
