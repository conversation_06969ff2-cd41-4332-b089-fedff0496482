"""
SEO Metadata Generation Service for Emma Studio
Automatically generates optimized SEO metadata using transformer models
"""

import logging
import re
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime

import google.generativeai as genai
from transformers import pipeline, AutoTokenizer, AutoModelForSeq2SeqLM

from app.core.config import settings

logger = logging.getLogger(__name__)

class SEOMetadataService:
    """Service for generating SEO-optimized metadata using AI models."""

    def __init__(self):
        self.gemini_model = None
        self.summarization_pipeline = None
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.initialized = False
        self._is_shutdown = False

        # SEO best practices constants
        self.TITLE_MAX_LENGTH = 60
        self.DESCRIPTION_MAX_LENGTH = 160
        self.KEYWORDS_MAX_COUNT = 10

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with proper cleanup."""
        self.shutdown()

    def shutdown(self):
        """Properly shutdown the thread pool executor."""
        if not self._is_shutdown and self.executor:
            try:
                logger.info("Shutting down SEOMetadataService thread pool...")
                self.executor.shutdown(wait=True)
                self._is_shutdown = True
                logger.info("✅ SEOMetadataService thread pool shutdown complete")
            except Exception as e:
                logger.error(f"❌ Error shutting down SEOMetadataService thread pool: {e}")

    def __del__(self):
        """Destructor to ensure cleanup."""
        self.shutdown()
        
    async def initialize(self):
        """Initialize the SEO metadata service."""
        try:
            if self._is_shutdown:
                logger.error("Cannot initialize: SEOMetadataService is shutdown")
                return False

            # Initialize Gemini
            if settings.GEMINI_API_KEY:
                genai.configure(api_key=settings.GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel('gemini-pro')
                logger.info("Gemini model initialized for SEO metadata generation")

            # Initialize summarization pipeline in thread pool
            loop = asyncio.get_event_loop()
            try:
                self.summarization_pipeline = await loop.run_in_executor(
                    self.executor,
                    self._load_summarization_model
                )
            except Exception as e:
                logger.error(f"Failed to load summarization model in thread pool: {e}")
                raise

            self.initialized = True
            logger.info("SEO metadata service initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize SEO metadata service: {e}")
            return False
    
    def _load_summarization_model(self):
        """Load the summarization model."""
        try:
            # Use a lightweight model for summarization
            return pipeline(
                "summarization",
                model="facebook/bart-large-cnn",
                tokenizer="facebook/bart-large-cnn",
                device=-1  # Use CPU
            )
        except Exception as e:
            logger.warning(f"Failed to load BART model, falling back to basic summarization: {e}")
            return None
    
    async def generate_seo_metadata(self, content: str, title: str = None, 
                                  target_keywords: List[str] = None,
                                  language: str = "es") -> Dict[str, Any]:
        """
        Generate comprehensive SEO metadata for content.
        
        Args:
            content: The main content text
            title: Optional existing title
            target_keywords: Optional list of target keywords
            language: Target language (default: Spanish)
            
        Returns:
            Dictionary containing SEO metadata
        """
        try:
            if not self.initialized:
                await self.initialize()
            
            # Clean and prepare content
            clean_content = self._clean_content(content)
            
            # Generate metadata components
            tasks = [
                self._generate_title(clean_content, title, target_keywords, language),
                self._generate_description(clean_content, target_keywords, language),
                self._extract_keywords(clean_content, target_keywords, language),
                self._generate_headings_structure(clean_content, language),
                self._analyze_content_structure(clean_content)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            generated_title = results[0] if not isinstance(results[0], Exception) else title or "Título generado"
            description = results[1] if not isinstance(results[1], Exception) else "Descripción generada automáticamente"
            keywords = results[2] if not isinstance(results[2], Exception) else []
            headings = results[3] if not isinstance(results[3], Exception) else []
            structure_analysis = results[4] if not isinstance(results[4], Exception) else {}
            
            # Generate additional SEO elements
            schema_markup = await self._generate_schema_markup(
                generated_title, description, clean_content, language
            )
            
            return {
                "title": generated_title,
                "meta_description": description,
                "keywords": keywords,
                "suggested_headings": headings,
                "schema_markup": schema_markup,
                "content_analysis": structure_analysis,
                "seo_score": self._calculate_seo_score(
                    generated_title, description, keywords, clean_content
                ),
                "optimization_suggestions": self._generate_optimization_suggestions(
                    generated_title, description, keywords, clean_content
                ),
                "generated_at": datetime.utcnow().isoformat(),
                "language": language
            }
            
        except Exception as e:
            logger.error(f"Failed to generate SEO metadata: {e}")
            return {
                "title": title or "Error en generación",
                "meta_description": "Error al generar descripción",
                "keywords": [],
                "error": str(e)
            }
    
    def _clean_content(self, content: str) -> str:
        """Clean and normalize content text."""
        # Remove HTML tags
        content = re.sub(r'<[^>]+>', '', content)
        # Remove extra whitespace
        content = re.sub(r'\s+', ' ', content).strip()
        # Limit content length for processing
        return content[:5000] if len(content) > 5000 else content
    
    async def _generate_title(self, content: str, existing_title: str = None,
                            keywords: List[str] = None, language: str = "es") -> str:
        """Generate SEO-optimized title."""
        try:
            if existing_title and len(existing_title) <= self.TITLE_MAX_LENGTH:
                return existing_title
            
            if self.gemini_model:
                keyword_text = f" Incluye estas palabras clave: {', '.join(keywords)}" if keywords else ""
                
                prompt = f"""
                Genera un título SEO optimizado para el siguiente contenido en {language}.
                El título debe:
                - Tener máximo {self.TITLE_MAX_LENGTH} caracteres
                - Ser atractivo y descriptivo
                - Incluir palabras clave relevantes{keyword_text}
                - Ser único y llamativo
                
                Contenido:
                {content[:1000]}
                
                Responde solo con el título, sin explicaciones adicionales.
                """
                
                response = await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    lambda: self.gemini_model.generate_content(prompt)
                )
                
                title = response.text.strip().strip('"').strip("'")
                return title[:self.TITLE_MAX_LENGTH] if len(title) > self.TITLE_MAX_LENGTH else title
            
            # Fallback: extract first sentence or create basic title
            sentences = content.split('.')
            if sentences:
                title = sentences[0].strip()
                return title[:self.TITLE_MAX_LENGTH] if len(title) > self.TITLE_MAX_LENGTH else title
            
            return "Contenido Optimizado SEO"
            
        except Exception as e:
            logger.error(f"Failed to generate title: {e}")
            return existing_title or "Título Generado"
    
    async def _generate_description(self, content: str, keywords: List[str] = None,
                                  language: str = "es") -> str:
        """Generate SEO-optimized meta description."""
        try:
            if self.gemini_model:
                keyword_text = f" Incluye estas palabras clave: {', '.join(keywords)}" if keywords else ""
                
                prompt = f"""
                Genera una meta descripción SEO optimizada para el siguiente contenido en {language}.
                La descripción debe:
                - Tener máximo {self.DESCRIPTION_MAX_LENGTH} caracteres
                - Ser persuasiva y descriptiva
                - Incluir palabras clave relevantes{keyword_text}
                - Motivar al clic
                
                Contenido:
                {content[:1500]}
                
                Responde solo con la descripción, sin explicaciones adicionales.
                """
                
                response = await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    lambda: self.gemini_model.generate_content(prompt)
                )
                
                description = response.text.strip().strip('"').strip("'")
                return description[:self.DESCRIPTION_MAX_LENGTH] if len(description) > self.DESCRIPTION_MAX_LENGTH else description
            
            # Fallback: use summarization pipeline or extract first paragraph
            if self.summarization_pipeline and len(content) > 200:
                try:
                    loop = asyncio.get_event_loop()
                    summary = await loop.run_in_executor(
                        self.executor,
                        lambda: self.summarization_pipeline(content[:1024], max_length=50, min_length=20, do_sample=False)
                    )
                    description = summary[0]['summary_text']
                    return description[:self.DESCRIPTION_MAX_LENGTH] if len(description) > self.DESCRIPTION_MAX_LENGTH else description
                except Exception as e:
                    logger.warning(f"Summarization failed: {e}")
            
            # Basic fallback
            sentences = content.split('.')[:2]
            description = '. '.join(sentences).strip()
            return description[:self.DESCRIPTION_MAX_LENGTH] if len(description) > self.DESCRIPTION_MAX_LENGTH else description
            
        except Exception as e:
            logger.error(f"Failed to generate description: {e}")
            return "Descripción generada automáticamente para contenido optimizado SEO."
    
    async def _extract_keywords(self, content: str, target_keywords: List[str] = None,
                              language: str = "es") -> List[str]:
        """Extract and suggest SEO keywords."""
        try:
            if self.gemini_model:
                target_text = f" Prioriza estas palabras clave: {', '.join(target_keywords)}" if target_keywords else ""
                
                prompt = f"""
                Extrae las {self.KEYWORDS_MAX_COUNT} palabras clave más importantes para SEO del siguiente contenido en {language}.
                {target_text}
                
                Contenido:
                {content[:2000]}
                
                Responde solo con las palabras clave separadas por comas, sin explicaciones.
                """
                
                response = await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    lambda: self.gemini_model.generate_content(prompt)
                )
                
                keywords_text = response.text.strip()
                keywords = [kw.strip() for kw in keywords_text.split(',')]
                return keywords[:self.KEYWORDS_MAX_COUNT]
            
            # Fallback: basic keyword extraction
            words = re.findall(r'\b[a-záéíóúñ]{4,}\b', content.lower())
            word_freq = {}
            for word in words:
                word_freq[word] = word_freq.get(word, 0) + 1
            
            # Get most frequent words
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            keywords = [word for word, freq in sorted_words[:self.KEYWORDS_MAX_COUNT] if freq > 1]
            
            # Include target keywords if provided
            if target_keywords:
                keywords = target_keywords + [kw for kw in keywords if kw not in target_keywords]
            
            return keywords[:self.KEYWORDS_MAX_COUNT]
            
        except Exception as e:
            logger.error(f"Failed to extract keywords: {e}")
            return target_keywords or []
    
    async def _generate_headings_structure(self, content: str, language: str = "es") -> List[Dict[str, str]]:
        """Generate suggested heading structure."""
        try:
            if self.gemini_model:
                prompt = f"""
                Sugiere una estructura de encabezados (H1, H2, H3) para el siguiente contenido en {language}.
                Responde en formato JSON con esta estructura:
                [
                    {{"level": "H1", "text": "Título principal"}},
                    {{"level": "H2", "text": "Subtítulo"}},
                    {{"level": "H3", "text": "Sub-subtítulo"}}
                ]
                
                Contenido:
                {content[:2000]}
                """
                
                response = await asyncio.get_event_loop().run_in_executor(
                    self.executor,
                    lambda: self.gemini_model.generate_content(prompt)
                )
                
                # Try to parse JSON response
                import json
                try:
                    headings = json.loads(response.text.strip())
                    return headings if isinstance(headings, list) else []
                except json.JSONDecodeError:
                    pass
            
            # Fallback: basic heading structure
            paragraphs = content.split('\n\n')[:5]
            headings = []
            for i, para in enumerate(paragraphs):
                if para.strip():
                    level = "H1" if i == 0 else "H2"
                    text = para.split('.')[0].strip()[:60]
                    headings.append({"level": level, "text": text})
            
            return headings
            
        except Exception as e:
            logger.error(f"Failed to generate headings: {e}")
            return []
    
    async def _analyze_content_structure(self, content: str) -> Dict[str, Any]:
        """Analyze content structure for SEO insights."""
        try:
            word_count = len(content.split())
            char_count = len(content)
            paragraph_count = len([p for p in content.split('\n\n') if p.strip()])
            sentence_count = len([s for s in content.split('.') if s.strip()])
            
            # Reading time estimation (average 200 words per minute)
            reading_time = max(1, round(word_count / 200))
            
            return {
                "word_count": word_count,
                "character_count": char_count,
                "paragraph_count": paragraph_count,
                "sentence_count": sentence_count,
                "estimated_reading_time_minutes": reading_time,
                "content_density": "high" if word_count > 1000 else "medium" if word_count > 500 else "low"
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze content structure: {e}")
            return {}
    
    async def _generate_schema_markup(self, title: str, description: str, 
                                    content: str, language: str = "es") -> Dict[str, Any]:
        """Generate basic Schema.org markup."""
        try:
            return {
                "@context": "https://schema.org",
                "@type": "Article",
                "headline": title,
                "description": description,
                "inLanguage": language,
                "wordCount": len(content.split()),
                "datePublished": datetime.utcnow().isoformat(),
                "dateModified": datetime.utcnow().isoformat(),
                "author": {
                    "@type": "Organization",
                    "name": "Emma Studio AI"
                },
                "publisher": {
                    "@type": "Organization",
                    "name": "Emma Studio"
                }
            }
        except Exception as e:
            logger.error(f"Failed to generate schema markup: {e}")
            return {}
    
    def _calculate_seo_score(self, title: str, description: str, 
                           keywords: List[str], content: str) -> Dict[str, Any]:
        """Calculate basic SEO score."""
        try:
            score = 0
            max_score = 100
            factors = {}
            
            # Title optimization (20 points)
            if title and len(title) <= self.TITLE_MAX_LENGTH:
                title_score = 20
                factors["title_length"] = "optimal"
            elif title and len(title) <= self.TITLE_MAX_LENGTH + 10:
                title_score = 15
                factors["title_length"] = "acceptable"
            else:
                title_score = 5
                factors["title_length"] = "too_long"
            score += title_score
            
            # Description optimization (20 points)
            if description and len(description) <= self.DESCRIPTION_MAX_LENGTH:
                desc_score = 20
                factors["description_length"] = "optimal"
            elif description and len(description) <= self.DESCRIPTION_MAX_LENGTH + 20:
                desc_score = 15
                factors["description_length"] = "acceptable"
            else:
                desc_score = 5
                factors["description_length"] = "too_long"
            score += desc_score
            
            # Keywords presence (30 points)
            if keywords:
                keyword_score = min(30, len(keywords) * 3)
                factors["keywords_count"] = len(keywords)
            else:
                keyword_score = 0
                factors["keywords_count"] = 0
            score += keyword_score
            
            # Content length (30 points)
            word_count = len(content.split())
            if word_count >= 1000:
                content_score = 30
                factors["content_length"] = "excellent"
            elif word_count >= 500:
                content_score = 20
                factors["content_length"] = "good"
            elif word_count >= 300:
                content_score = 10
                factors["content_length"] = "acceptable"
            else:
                content_score = 5
                factors["content_length"] = "too_short"
            score += content_score
            
            return {
                "total_score": score,
                "max_score": max_score,
                "percentage": round((score / max_score) * 100, 1),
                "grade": self._get_grade(score, max_score),
                "factors": factors
            }
            
        except Exception as e:
            logger.error(f"Failed to calculate SEO score: {e}")
            return {"total_score": 0, "max_score": 100, "percentage": 0, "grade": "F"}
    
    def _get_grade(self, score: int, max_score: int) -> str:
        """Convert score to letter grade."""
        percentage = (score / max_score) * 100
        if percentage >= 90:
            return "A"
        elif percentage >= 80:
            return "B"
        elif percentage >= 70:
            return "C"
        elif percentage >= 60:
            return "D"
        else:
            return "F"
    
    def _generate_optimization_suggestions(self, title: str, description: str,
                                         keywords: List[str], content: str) -> List[str]:
        """Generate SEO optimization suggestions."""
        suggestions = []
        
        try:
            # Title suggestions
            if not title or len(title) > self.TITLE_MAX_LENGTH:
                suggestions.append(f"Optimiza el título para que tenga máximo {self.TITLE_MAX_LENGTH} caracteres")
            
            # Description suggestions
            if not description or len(description) > self.DESCRIPTION_MAX_LENGTH:
                suggestions.append(f"Optimiza la meta descripción para que tenga máximo {self.DESCRIPTION_MAX_LENGTH} caracteres")
            
            # Keywords suggestions
            if not keywords or len(keywords) < 3:
                suggestions.append("Añade más palabras clave relevantes (mínimo 3-5)")
            
            # Content length suggestions
            word_count = len(content.split())
            if word_count < 300:
                suggestions.append("Aumenta la longitud del contenido (mínimo 300 palabras)")
            elif word_count < 500:
                suggestions.append("Considera expandir el contenido para mejor SEO (500+ palabras)")
            
            # Structure suggestions
            if '\n\n' not in content:
                suggestions.append("Divide el contenido en párrafos para mejor legibilidad")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Failed to generate suggestions: {e}")
            return ["Error al generar sugerencias de optimización"]

# Global instance
seo_metadata_service = SEOMetadataService()
