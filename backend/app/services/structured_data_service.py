"""
Structured Data Service for Emma Studio
Generates Schema.org JSON-LD markup for better LLM discoverability
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timezone
from urllib.parse import urljoin

from app.core.config import settings

logger = logging.getLogger(__name__)

class StructuredDataService:
    """Service for generating Schema.org structured data markup."""
    
    def __init__(self):
        self.base_url = getattr(settings, 'BASE_URL', 'https://emmastudio.ai')
        self.organization_info = {
            "name": "Emma Studio",
            "description": "Plataforma de inteligencia artificial para marketing y creación de contenido",
            "url": self.base_url,
            "logo": f"{self.base_url}/logo.png",
            "sameAs": [
                "https://twitter.com/emmastudio",
                "https://linkedin.com/company/emmastudio"
            ]
        }
    
    def generate_article_schema(self, 
                              title: str,
                              content: str,
                              description: str = None,
                              keywords: List[str] = None,
                              author: str = "Emma Studio AI",
                              published_date: datetime = None,
                              modified_date: datetime = None,
                              url: str = None,
                              image_url: str = None,
                              language: str = "es") -> Dict[str, Any]:
        """
        Generate Article schema markup.
        
        Args:
            title: Article title
            content: Article content
            description: Article description
            keywords: List of keywords
            author: Author name
            published_date: Publication date
            modified_date: Last modification date
            url: Article URL
            image_url: Featured image URL
            language: Content language
            
        Returns:
            Schema.org Article JSON-LD markup
        """
        try:
            now = datetime.now(timezone.utc)
            published = published_date or now
            modified = modified_date or now
            
            # Calculate reading time (average 200 words per minute)
            word_count = len(content.split())
            reading_time = max(1, round(word_count / 200))
            
            schema = {
                "@context": "https://schema.org",
                "@type": "Article",
                "headline": title,
                "description": description or title,
                "inLanguage": language,
                "wordCount": word_count,
                "timeRequired": f"PT{reading_time}M",
                "datePublished": published.isoformat(),
                "dateModified": modified.isoformat(),
                "author": {
                    "@type": "Organization" if "AI" in author else "Person",
                    "name": author
                },
                "publisher": {
                    "@type": "Organization",
                    "name": self.organization_info["name"],
                    "description": self.organization_info["description"],
                    "url": self.organization_info["url"],
                    "logo": {
                        "@type": "ImageObject",
                        "url": self.organization_info["logo"]
                    },
                    "sameAs": self.organization_info["sameAs"]
                }
            }
            
            # Add optional fields
            if url:
                schema["url"] = url
                schema["mainEntityOfPage"] = {
                    "@type": "WebPage",
                    "@id": url
                }
            
            if image_url:
                schema["image"] = {
                    "@type": "ImageObject",
                    "url": image_url,
                    "width": 1200,
                    "height": 630
                }
            
            if keywords:
                schema["keywords"] = keywords
            
            return schema
            
        except Exception as e:
            logger.error(f"Failed to generate Article schema: {e}")
            return {}
    
    def generate_faq_schema(self, 
                          questions_answers: List[Dict[str, str]],
                          title: str = None,
                          url: str = None) -> Dict[str, Any]:
        """
        Generate FAQPage schema markup.
        
        Args:
            questions_answers: List of Q&A pairs with 'question' and 'answer' keys
            title: FAQ page title
            url: FAQ page URL
            
        Returns:
            Schema.org FAQPage JSON-LD markup
        """
        try:
            if not questions_answers:
                return {}
            
            schema = {
                "@context": "https://schema.org",
                "@type": "FAQPage",
                "mainEntity": []
            }
            
            if title:
                schema["name"] = title
            
            if url:
                schema["url"] = url
            
            # Add Q&A pairs
            for qa in questions_answers:
                if "question" in qa and "answer" in qa:
                    faq_item = {
                        "@type": "Question",
                        "name": qa["question"],
                        "acceptedAnswer": {
                            "@type": "Answer",
                            "text": qa["answer"]
                        }
                    }
                    schema["mainEntity"].append(faq_item)
            
            return schema
            
        except Exception as e:
            logger.error(f"Failed to generate FAQ schema: {e}")
            return {}
    
    def generate_organization_schema(self, 
                                   additional_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate Organization schema markup.
        
        Args:
            additional_info: Additional organization information
            
        Returns:
            Schema.org Organization JSON-LD markup
        """
        try:
            schema = {
                "@context": "https://schema.org",
                "@type": "Organization",
                "name": self.organization_info["name"],
                "description": self.organization_info["description"],
                "url": self.organization_info["url"],
                "logo": {
                    "@type": "ImageObject",
                    "url": self.organization_info["logo"]
                },
                "sameAs": self.organization_info["sameAs"],
                "foundingDate": "2024",
                "industry": "Artificial Intelligence",
                "knowsAbout": [
                    "Artificial Intelligence",
                    "Marketing Digital",
                    "Generación de Contenido",
                    "SEO",
                    "Automatización de Marketing"
                ]
            }
            
            # Add additional information if provided
            if additional_info:
                schema.update(additional_info)
            
            return schema
            
        except Exception as e:
            logger.error(f"Failed to generate Organization schema: {e}")
            return {}
    
    def generate_website_schema(self, 
                              site_name: str = None,
                              description: str = None,
                              url: str = None) -> Dict[str, Any]:
        """
        Generate WebSite schema markup.
        
        Args:
            site_name: Website name
            description: Website description
            url: Website URL
            
        Returns:
            Schema.org WebSite JSON-LD markup
        """
        try:
            schema = {
                "@context": "https://schema.org",
                "@type": "WebSite",
                "name": site_name or self.organization_info["name"],
                "description": description or self.organization_info["description"],
                "url": url or self.organization_info["url"],
                "publisher": {
                    "@type": "Organization",
                    "name": self.organization_info["name"],
                    "url": self.organization_info["url"]
                },
                "potentialAction": {
                    "@type": "SearchAction",
                    "target": {
                        "@type": "EntryPoint",
                        "urlTemplate": f"{self.base_url}/search?q={{search_term_string}}"
                    },
                    "query-input": "required name=search_term_string"
                }
            }
            
            return schema
            
        except Exception as e:
            logger.error(f"Failed to generate WebSite schema: {e}")
            return {}
    
    def generate_breadcrumb_schema(self, 
                                 breadcrumbs: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        Generate BreadcrumbList schema markup.
        
        Args:
            breadcrumbs: List of breadcrumb items with 'name' and 'url' keys
            
        Returns:
            Schema.org BreadcrumbList JSON-LD markup
        """
        try:
            if not breadcrumbs:
                return {}
            
            schema = {
                "@context": "https://schema.org",
                "@type": "BreadcrumbList",
                "itemListElement": []
            }
            
            for i, crumb in enumerate(breadcrumbs):
                if "name" in crumb:
                    item = {
                        "@type": "ListItem",
                        "position": i + 1,
                        "name": crumb["name"]
                    }
                    
                    if "url" in crumb:
                        item["item"] = crumb["url"]
                    
                    schema["itemListElement"].append(item)
            
            return schema
            
        except Exception as e:
            logger.error(f"Failed to generate Breadcrumb schema: {e}")
            return {}
    
    def generate_software_application_schema(self, 
                                           app_name: str = "Emma Studio",
                                           description: str = None,
                                           features: List[str] = None) -> Dict[str, Any]:
        """
        Generate SoftwareApplication schema markup.
        
        Args:
            app_name: Application name
            description: Application description
            features: List of application features
            
        Returns:
            Schema.org SoftwareApplication JSON-LD markup
        """
        try:
            schema = {
                "@context": "https://schema.org",
                "@type": "SoftwareApplication",
                "name": app_name,
                "description": description or "Plataforma de IA para marketing y creación de contenido",
                "url": self.organization_info["url"],
                "applicationCategory": "BusinessApplication",
                "operatingSystem": "Web Browser",
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "EUR",
                    "availability": "https://schema.org/InStock"
                },
                "publisher": {
                    "@type": "Organization",
                    "name": self.organization_info["name"],
                    "url": self.organization_info["url"]
                }
            }
            
            if features:
                schema["featureList"] = features
            
            return schema
            
        except Exception as e:
            logger.error(f"Failed to generate SoftwareApplication schema: {e}")
            return {}
    
    def combine_schemas(self, *schemas: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Combine multiple schema objects into a list.
        
        Args:
            *schemas: Variable number of schema dictionaries
            
        Returns:
            List of valid schema objects
        """
        try:
            valid_schemas = []
            
            for schema in schemas:
                if schema and isinstance(schema, dict) and "@type" in schema:
                    valid_schemas.append(schema)
            
            return valid_schemas
            
        except Exception as e:
            logger.error(f"Failed to combine schemas: {e}")
            return []
    
    def generate_json_ld_script(self, schemas: Union[Dict[str, Any], List[Dict[str, Any]]]) -> str:
        """
        Generate JSON-LD script tag for HTML insertion.
        
        Args:
            schemas: Single schema or list of schemas
            
        Returns:
            HTML script tag with JSON-LD content
        """
        try:
            if not schemas:
                return ""
            
            # Ensure schemas is a list
            if isinstance(schemas, dict):
                schemas = [schemas]
            
            # Filter out empty schemas
            valid_schemas = [s for s in schemas if s and isinstance(s, dict)]
            
            if not valid_schemas:
                return ""
            
            # If single schema, use it directly; otherwise use array
            json_content = valid_schemas[0] if len(valid_schemas) == 1 else valid_schemas
            
            json_str = json.dumps(json_content, ensure_ascii=False, indent=2)
            
            return f'<script type="application/ld+json">\n{json_str}\n</script>'
            
        except Exception as e:
            logger.error(f"Failed to generate JSON-LD script: {e}")
            return ""
    
    def extract_faq_from_content(self, content: str) -> List[Dict[str, str]]:
        """
        Extract FAQ pairs from content text.
        
        Args:
            content: Content text to analyze
            
        Returns:
            List of Q&A pairs
        """
        try:
            import re
            
            # Look for question patterns
            question_patterns = [
                r'¿([^?]+)\?',  # Spanish questions
                r'([^.!?]*\?)',  # General questions ending with ?
                r'P:\s*([^R]+)',  # P: Q format
                r'Pregunta:\s*([^R]+)',  # Pregunta: format
            ]
            
            # Look for answer patterns
            answer_patterns = [
                r'R:\s*([^P]+)',  # R: A format
                r'Respuesta:\s*([^P]+)',  # Respuesta: format
            ]
            
            questions = []
            answers = []
            
            # Extract questions
            for pattern in question_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                questions.extend([q.strip() for q in matches if q.strip()])
            
            # Extract answers
            for pattern in answer_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                answers.extend([a.strip() for a in matches if a.strip()])
            
            # Pair questions with answers
            faq_pairs = []
            min_length = min(len(questions), len(answers))
            
            for i in range(min_length):
                if questions[i] and answers[i]:
                    faq_pairs.append({
                        "question": questions[i],
                        "answer": answers[i]
                    })
            
            return faq_pairs
            
        except Exception as e:
            logger.error(f"Failed to extract FAQ from content: {e}")
            return []

# Global instance
structured_data_service = StructuredDataService()
