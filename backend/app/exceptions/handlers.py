"""Exception handlers for the application."""

import logging
import uuid
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse

from app.core.error_responses import StandardErrorResponse, ErrorCode

logger = logging.getLogger(__name__)

async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions with standardized format."""
    request_id = getattr(request.state, "request_id", str(uuid.uuid4())[:8])

    # Check if the exception detail is already in standardized format
    if isinstance(exc.detail, dict) and "status" in exc.detail:
        # Already standardized, just add request_id if missing
        if "error" in exc.detail and "request_id" not in exc.detail["error"]:
            exc.detail["error"]["request_id"] = request_id
        return JSONResponse(status_code=exc.status_code, content=exc.detail)

    # Convert to standardized format
    error_code = ErrorCode.INTERNAL_SERVER_ERROR
    message = str(exc.detail)

    # Map status codes to appropriate error codes and Spanish messages
    if exc.status_code == 400:
        error_code = ErrorCode.VALIDATION_ERROR
        message = "Los datos proporcionados no son válidos"
    elif exc.status_code == 401:
        error_code = ErrorCode.AUTHENTICATION_FAILED
        message = "Autenticación requerida"
    elif exc.status_code == 403:
        error_code = ErrorCode.INSUFFICIENT_PERMISSIONS
        message = "Permisos insuficientes"
    elif exc.status_code == 404:
        error_code = ErrorCode.RESOURCE_NOT_FOUND
        message = "El recurso solicitado no fue encontrado"
    elif exc.status_code == 429:
        error_code = ErrorCode.RATE_LIMIT_EXCEEDED
        message = "Límite de solicitudes excedido"
    elif exc.status_code == 503:
        error_code = ErrorCode.EXTERNAL_SERVICE_UNAVAILABLE
        message = "Servicio temporalmente no disponible"

    content = StandardErrorResponse.create_error_response(
        status_code=exc.status_code,
        error_code=error_code,
        message=message,
        details=str(exc.detail) if str(exc.detail) != message else None,
        request_id=request_id
    )

    return JSONResponse(status_code=exc.status_code, content=content)

async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle validation exceptions with standardized format."""
    request_id = getattr(request.state, "request_id", str(uuid.uuid4())[:8])

    logger.error(f"Validation error for request {request_id}", exc_info=exc)

    # Extract field-specific errors
    field_errors = {}
    for error in exc.errors():
        field_path = " -> ".join(str(loc) for loc in error["loc"])
        field_errors[field_path] = error["msg"]

    content = StandardErrorResponse.create_error_response(
        status_code=422,
        error_code=ErrorCode.VALIDATION_ERROR,
        message="Los datos proporcionados contienen errores de validación",
        details="Revisa los campos marcados y corrige los errores",
        request_id=request_id,
        field_errors=field_errors
    )

    return JSONResponse(status_code=422, content=content)

async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle generic exceptions with standardized format."""
    request_id = getattr(request.state, "request_id", str(uuid.uuid4())[:8])

    logger.error(f"Unhandled exception for request {request_id}", exc_info=exc)

    # Determine error type and message based on exception
    error_code = ErrorCode.INTERNAL_SERVER_ERROR
    message = "Error interno del servidor. Por favor contacta al soporte técnico"
    details = str(exc)

    # Check for specific exception types
    if "database" in str(exc).lower() or "sql" in str(exc).lower():
        error_code = ErrorCode.DATABASE_ERROR
        message = "Error de base de datos. Inténtalo de nuevo más tarde"
    elif "timeout" in str(exc).lower():
        error_code = ErrorCode.EXTERNAL_SERVICE_TIMEOUT
        message = "La operación tardó demasiado tiempo. Inténtalo de nuevo"
    elif "connection" in str(exc).lower():
        error_code = ErrorCode.EXTERNAL_SERVICE_ERROR
        message = "Error de conexión. Verifica tu conexión a internet"

    content = StandardErrorResponse.create_error_response(
        status_code=500,
        error_code=error_code,
        message=message,
        details=details,
        request_id=request_id
    )

    return JSONResponse(status_code=500, content=content)
