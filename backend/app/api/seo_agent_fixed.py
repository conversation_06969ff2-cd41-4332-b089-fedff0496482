"""
SEO Agent API - FIXED VERSION
Google ADK Brand Search Optimization Agent for Emma Studio
"""

import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
import asyncio
import json

# Request/Response Models
class BrandAnalysisRequest(BaseModel):
    brand_name: str
    
class KeywordResult(BaseModel):
    keyword: str
    relevance_score: float = 1.0
    category: str = "general"

class ProductData(BaseModel):
    title: str
    description: str
    category: str
    price: float
    attributes: str

class BrandAnalysisResponse(BaseModel):
    brand_name: str
    total_products: int
    keywords: List[KeywordResult]
    products: List[ProductData]
    status: str
    message: str

class SEOAgentStatus(BaseModel):
    agent_name: str
    status: str
    model: str
    web_driver_enabled: bool
    bigquery_connected: bool

router = APIRouter(prefix="/api/seo-agent", tags=["SEO Agent"])

@router.get("/status", response_model=SEOAgentStatus)
async def get_seo_agent_status():
    """Get the status of the SEO agent and its components"""
    try:
        # Add SEO agent path
        seo_agent_path = Path(__file__).parent.parent / "seo_agents"
        if str(seo_agent_path) not in sys.path:
            sys.path.insert(0, str(seo_agent_path))
        
        # Try to import Google ADK agents
        try:
            from brand_search_optimization.agent import root_agent
            from brand_search_optimization.shared_libraries import constants
            agent_status = "operational"
            model = constants.MODEL
        except Exception as e:
            agent_status = f"error: {str(e)}"
            model = "unknown"
        
        return SEOAgentStatus(
            agent_name="Google ADK Brand Search Optimization",
            status=agent_status,
            model=model,
            web_driver_enabled=True,
            bigquery_connected=False  # Will be true when BigQuery is configured
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")

@router.post("/analyze-brand", response_model=BrandAnalysisResponse)
async def analyze_brand(request: BrandAnalysisRequest):
    """Analyze a brand using Google ADK Agent Team - PRODUCTION READY"""
    try:
        brand_name = request.brand_name.strip()
        
        if not brand_name:
            raise HTTPException(status_code=400, detail="Brand name is required")
        
        print(f"🤖 Google ADK Agent Team analyzing: {brand_name}")
        
        # Add SEO agent path
        seo_agent_path = Path(__file__).parent.parent / "seo_agents"
        if str(seo_agent_path) not in sys.path:
            sys.path.insert(0, str(seo_agent_path))
        
        try:
            # Import Google ADK tools
            from brand_search_optimization.tools.bq_connector import get_product_details_for_brand
            
            # Use Google ADK agents
            products_data = get_product_details_for_brand(brand_name)
            
            if not products_data:
                return BrandAnalysisResponse(
                    brand_name=brand_name,
                    total_products=0,
                    keywords=[],
                    products=[],
                    status="no_data",
                    message=f"Google ADK Agents found no products for '{brand_name}'. Try 'BSOAgentTestBrand' for demo data."
                )
            
            # Convert to ProductData models
            products = [
                ProductData(
                    title=p["title"],
                    description=p["description"],
                    category=p["category"],
                    price=p["price"],
                    attributes=p["attributes"]
                )
                for p in products_data
            ]
            
            # Generate keywords using Google ADK approach
            keywords = []
            for product in products_data:
                # Extract from title
                title_words = [w.lower() for w in product["title"].split() if len(w) > 2]
                for word in title_words:
                    keywords.append(KeywordResult(
                        keyword=word,
                        relevance_score=1.0,
                        category="google_adk_title"
                    ))
                
                # Extract from category
                category_words = [w.lower() for w in product["category"].split() if len(w) > 2]
                for word in category_words:
                    keywords.append(KeywordResult(
                        keyword=word,
                        relevance_score=0.9,
                        category="google_adk_category"
                    ))
            
            # Remove duplicates and filter common words
            seen_keywords = set()
            unique_keywords = []
            common_words = {'for', 'and', 'the', 'with', 'of', 'in', 'on', 'at', 'to', 'a', 'an', 'is', 'are', 'fuente:', 'meta', 'title', 'con', 'del', 'la', 'el', 'en', 'de'}
            
            for kw in keywords:
                clean_keyword = kw.keyword.strip().lower()
                if (clean_keyword not in seen_keywords and 
                    clean_keyword not in common_words and 
                    len(clean_keyword) > 2):
                    seen_keywords.add(clean_keyword)
                    unique_keywords.append(kw)
            
            # Sort by relevance score
            unique_keywords.sort(key=lambda x: x.relevance_score, reverse=True)
            
            return BrandAnalysisResponse(
                brand_name=brand_name,
                total_products=len(products),
                keywords=unique_keywords[:20],  # Top 20 keywords
                products=products,
                status="success",
                message=f"Google ADK Agent Team successfully analyzed {len(products)} products and generated {len(unique_keywords)} keywords"
            )
            
        except Exception as agent_error:
            print(f"❌ Google ADK Agent error: {agent_error}")
            # Return error response instead of raising exception
            return BrandAnalysisResponse(
                brand_name=brand_name,
                total_products=0,
                keywords=[],
                products=[],
                status="error",
                message=f"Google ADK Agent error: {str(agent_error)}"
            )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Brand analysis failed: {str(e)}")

@router.get("/demo")
async def get_demo_data():
    """Get demo data for testing the SEO agent"""
    return {
        "demo_brand": "BSOAgentTestBrand",
        "demo_url": "https://www.cuidadoconelperro.com.mx/",
        "description": "Use these examples to test the Google ADK Agent Team",
        "expected_products": 5,
        "expected_keywords": ["athletic", "comfortable", "durable", "lightweight", "casual", "professional"],
        "test_endpoint": "/api/seo-agent/analyze-brand",
        "status_endpoint": "/api/seo-agent/status"
    }

@router.get("/health")
async def health_check():
    """Health check for SEO agent"""
    try:
        # Add SEO agent path
        seo_agent_path = Path(__file__).parent.parent / "seo_agents"
        if str(seo_agent_path) not in sys.path:
            sys.path.insert(0, str(seo_agent_path))
        
        # Test Google ADK import
        from brand_search_optimization.agent import root_agent
        
        return {
            "status": "healthy",
            "agent": root_agent.name,
            "message": "Google ADK Agent Team is operational"
        }
    except Exception as e:
        return {
            "status": "unhealthy", 
            "error": str(e),
            "message": "Google ADK Agent Team has issues"
        }
