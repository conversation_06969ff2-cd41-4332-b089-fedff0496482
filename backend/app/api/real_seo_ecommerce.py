"""
Real SEO Ecommerce API for Emma Studio
Uses authentic data sources and follows Google's official SEO methodology
No simulated or fake data - only real analysis
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime

from ..services.real_seo_ecommerce_service import RealSEOEcommerceService, SEOEcommerceAnalysis

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Request/Response Models
class BrandAnalysisRequest(BaseModel):
    brand_name: str
    analysis_type: Optional[str] = "comprehensive"  # comprehensive, basic, technical
    include_competitors: Optional[bool] = False

class RealKeywordResult(BaseModel):
    keyword: str
    relevance_score: float
    source: str
    search_volume: str = "unknown"
    competition: str = "unknown"

class RealProductData(BaseModel):
    title: str
    description: str
    category: str
    brand: str
    price: Optional[float]
    currency: str = "USD"
    sku: Optional[str]
    availability: str
    url: str
    image_url: Optional[str]
    rating: Optional[float]
    review_count: Optional[int]

class TechnicalSEOMetrics(BaseModel):
    https_enabled: bool
    mobile_friendly: str
    page_speed: str
    structured_data: bool
    canonical_tags: bool
    meta_tags_optimized: bool
    images_optimized: bool
    internal_links: int
    external_links: int

class RealSEOAnalysisResponse(BaseModel):
    brand_name: str
    analysis_timestamp: datetime
    total_products: int
    real_products: List[RealProductData]
    seo_keywords: List[RealKeywordResult]
    technical_seo: TechnicalSEOMetrics
    content_analysis: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    recommendations: List[str]
    data_sources: List[str]
    status: str
    message: str

class ServiceStatusResponse(BaseModel):
    service_name: str
    status: str
    google_api_configured: bool
    serper_api_configured: bool
    bigquery_available: bool
    real_data_sources: List[str]

router = APIRouter(prefix="/api/real-seo-ecommerce", tags=["Real SEO Ecommerce"])

@router.get("/status", response_model=ServiceStatusResponse)
async def get_service_status():
    """Get the status of the Real SEO Ecommerce Service"""
    try:
        # Check service configuration
        async with RealSEOEcommerceService() as service:
            google_configured = bool(service.google_api_key)
            serper_configured = bool(service.serper_api_key)
            
            return ServiceStatusResponse(
                service_name="Real SEO Ecommerce Service",
                status="operational",
                google_api_configured=google_configured,
                serper_api_configured=serper_configured,
                bigquery_available=False,  # TODO: Check BigQuery connection
                real_data_sources=[
                    "Serper API",
                    "Google Custom Search",
                    "Intelligent Web Scraping",
                    "Structured Data Extraction",
                    "Technical SEO Analysis"
                ]
            )
    except Exception as e:
        logger.error(f"❌ Service status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Service status check failed: {str(e)}")

@router.post("/analyze", response_model=RealSEOAnalysisResponse)
async def analyze_ecommerce_brand(request: BrandAnalysisRequest):
    """
    Perform comprehensive real SEO analysis of an ecommerce brand
    
    This endpoint provides:
    - Real product extraction from websites
    - Authentic SEO keyword analysis using Serper API
    - Technical SEO audit following Google's guidelines
    - Performance metrics and actionable recommendations
    - No simulated data - only real analysis
    """
    try:
        brand_name = request.brand_name.strip()
        
        if not brand_name:
            raise HTTPException(status_code=400, detail="Brand name is required")
        
        logger.info(f"🔍 Starting REAL SEO analysis for: {brand_name}")
        
        # Use the Real SEO Ecommerce Service
        async with RealSEOEcommerceService() as seo_service:
            try:
                # Perform comprehensive real SEO analysis
                analysis_result = await seo_service.analyze_ecommerce_brand(brand_name)
                
                # Convert products to API format
                real_products = [
                    RealProductData(
                        title=product.title,
                        description=product.description,
                        category=product.category,
                        brand=product.brand,
                        price=product.price,
                        currency=product.currency,
                        sku=product.sku,
                        availability=product.availability,
                        url=product.url,
                        image_url=product.image_url,
                        rating=product.rating,
                        review_count=product.review_count
                    )
                    for product in analysis_result.real_products
                ]
                
                # Convert keywords to API format
                seo_keywords = [
                    RealKeywordResult(
                        keyword=kw["keyword"],
                        relevance_score=kw["relevance_score"],
                        source=kw["source"],
                        search_volume=kw.get("search_volume", "unknown"),
                        competition=kw.get("competition", "unknown")
                    )
                    for kw in analysis_result.seo_keywords
                ]
                
                # Convert technical SEO metrics
                tech_seo = analysis_result.technical_seo
                technical_metrics = TechnicalSEOMetrics(
                    https_enabled=tech_seo.get("https_enabled", False),
                    mobile_friendly=str(tech_seo.get("mobile_friendly", "unknown")),
                    page_speed=str(tech_seo.get("page_speed", "unknown")),
                    structured_data=tech_seo.get("structured_data", False),
                    canonical_tags=tech_seo.get("canonical_tags", False),
                    meta_tags_optimized=_check_meta_optimization(tech_seo.get("meta_tags", {})),
                    images_optimized=tech_seo.get("images_optimized", False),
                    internal_links=tech_seo.get("internal_links", 0),
                    external_links=tech_seo.get("external_links", 0)
                )
                
                # Determine status and message
                if analysis_result.total_products > 0:
                    status = "success"
                    message = f"✅ Real SEO analysis completed successfully! Found {analysis_result.total_products} products using {', '.join(analysis_result.data_sources)}"
                else:
                    status = "no_data"
                    message = f"❌ No products found for '{brand_name}'. This is authentic analysis - try a different brand name or website URL."
                
                return RealSEOAnalysisResponse(
                    brand_name=analysis_result.brand_name,
                    analysis_timestamp=analysis_result.analysis_timestamp,
                    total_products=analysis_result.total_products,
                    real_products=real_products,
                    seo_keywords=seo_keywords,
                    technical_seo=technical_metrics,
                    content_analysis=analysis_result.content_analysis,
                    performance_metrics=analysis_result.performance_metrics,
                    recommendations=analysis_result.recommendations,
                    data_sources=analysis_result.data_sources,
                    status=status,
                    message=message
                )
                
            except Exception as service_error:
                logger.error(f"❌ Real SEO Service error: {service_error}")
                raise HTTPException(
                    status_code=500, 
                    detail=f"SEO analysis failed: {str(service_error)}"
                )
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API Error: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

def _check_meta_optimization(meta_tags: Dict[str, Any]) -> bool:
    """Check if meta tags are optimized"""
    try:
        title_info = meta_tags.get("title", {})
        desc_info = meta_tags.get("description", {})
        
        title_optimal = title_info.get("optimal", False)
        desc_optimal = desc_info.get("optimal", False)
        
        return title_optimal and desc_optimal
    except:
        return False

@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Real SEO Ecommerce API",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@router.get("/supported-sources")
async def get_supported_data_sources():
    """Get list of supported real data sources"""
    return {
        "data_sources": [
            {
                "name": "Serper API",
                "description": "Real Google search results and keyword data",
                "type": "search_api"
            },
            {
                "name": "Intelligent Web Scraping",
                "description": "Advanced website content extraction",
                "type": "web_scraping"
            },
            {
                "name": "Structured Data Extraction",
                "description": "JSON-LD, microdata, and schema.org parsing",
                "type": "structured_data"
            },
            {
                "name": "Technical SEO Analysis",
                "description": "Real-time technical SEO audit",
                "type": "technical_analysis"
            },
            {
                "name": "Google Custom Search",
                "description": "Google Custom Search Engine integration",
                "type": "search_api"
            }
        ],
        "note": "All data sources provide authentic, real-time data - no simulated results"
    }
