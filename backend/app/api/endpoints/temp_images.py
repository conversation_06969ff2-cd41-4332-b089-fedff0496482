"""
Temporary image storage endpoint for Polotno editor
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import logging
from typing import Dict, Optional
import time
import httpx
import base64

logger = logging.getLogger(__name__)

router = APIRouter()

# In-memory storage for temporary images (in production, use Redis or similar)
temp_images: Dict[str, Dict] = {}

class TempImageRequest(BaseModel):
    id: str
    imageData: str

class TempImageResponse(BaseModel):
    success: bool
    message: str

class DownloadImageRequest(BaseModel):
    url: str
    id: Optional[str] = None

class DownloadImageResponse(BaseModel):
    success: bool
    message: str
    internal_url: Optional[str] = None
    image_id: Optional[str] = None

@router.post("/temp-images", response_model=TempImageResponse)
async def store_temp_image(request: TempImageRequest):
    """
    Store a temporary image for Polotno editor
    """
    try:
        logger.info(f"📥 Storing temporary image with ID: {request.id}")
        
        # Store image with timestamp for cleanup
        temp_images[request.id] = {
            'imageData': request.imageData,
            'timestamp': time.time()
        }
        
        # Clean up old images (older than 10 minutes)
        current_time = time.time()
        keys_to_remove = []
        for key, data in temp_images.items():
            if current_time - data['timestamp'] > 600:  # 10 minutes
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del temp_images[key]
            logger.info(f"🧹 Cleaned up old temporary image: {key}")
        
        logger.info(f"✅ Temporary image stored successfully: {request.id}")
        logger.info(f"📊 Total temporary images: {len(temp_images)}")
        
        return TempImageResponse(
            success=True,
            message="Image stored successfully"
        )
        
    except Exception as e:
        logger.error(f"❌ Error storing temporary image: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error storing temporary image: {str(e)}"
        )

@router.get("/temp-images/{image_id}")
async def get_temp_image(image_id: str):
    """
    Retrieve a temporary image by ID
    """
    try:
        logger.info(f"📤 Retrieving temporary image: {image_id}")
        
        if image_id not in temp_images:
            logger.error(f"❌ Temporary image not found: {image_id}")
            raise HTTPException(
                status_code=404,
                detail="Temporary image not found"
            )
        
        image_data = temp_images[image_id]['imageData']
        
        # Remove image after retrieval (one-time use)
        del temp_images[image_id]
        logger.info(f"✅ Temporary image retrieved and deleted: {image_id}")
        
        return {
            'success': True,
            'imageData': image_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error retrieving temporary image: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving temporary image: {str(e)}"
        )

@router.post("/download-and-store", response_model=DownloadImageResponse)
async def download_and_store_image(request: DownloadImageRequest):
    """
    Download an image from external URL and store it internally
    """
    try:
        logger.info(f"📥 Downloading image from URL: {request.url[:100]}...")

        # Generate ID if not provided
        image_id = request.id or f"downloaded_{int(time.time())}"

        # Download image from external URL
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(request.url)

            if response.status_code != 200:
                logger.error(f"❌ Failed to download image: {response.status_code}")
                raise HTTPException(
                    status_code=400,
                    detail=f"Failed to download image: HTTP {response.status_code}"
                )

            # Convert to base64
            image_content = response.content
            image_base64 = base64.b64encode(image_content).decode('utf-8')

            # Determine content type
            content_type = response.headers.get('content-type', 'image/png')
            if 'image/' not in content_type:
                content_type = 'image/png'

            # Create data URL
            image_data_url = f"data:{content_type};base64,{image_base64}"

            logger.info(f"✅ Image downloaded and converted to base64: {len(image_content)} bytes")

        # Store image with timestamp for cleanup
        temp_images[image_id] = {
            'imageData': image_data_url,
            'timestamp': time.time(),
            'originalUrl': request.url,
            'size': len(image_content)
        }

        # Clean up old images (older than 10 minutes)
        current_time = time.time()
        keys_to_remove = []
        for key, data in temp_images.items():
            if current_time - data['timestamp'] > 600:  # 10 minutes
                keys_to_remove.append(key)

        for key in keys_to_remove:
            del temp_images[key]
            logger.info(f"🧹 Cleaned up old downloaded image: {key}")

        # Create internal URL
        internal_url = f"/api/v1/temp-images/{image_id}"

        logger.info(f"✅ Image stored successfully with ID: {image_id}")
        logger.info(f"📊 Total stored images: {len(temp_images)}")

        return DownloadImageResponse(
            success=True,
            message="Image downloaded and stored successfully",
            internal_url=internal_url,
            image_id=image_id
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error downloading and storing image: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error downloading and storing image: {str(e)}"
        )
