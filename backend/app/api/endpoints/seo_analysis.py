"""SEO Analysis API endpoints."""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel, HttpUrl
import time
import uuid
import asyncio

from app.services.seo_analyzer import seo_analyzer
from app.services.persistent_seo_service import persistent_seo_service

logger = logging.getLogger(__name__)
router = APIRouter()

# Simple in-memory storage for progress tracking
# In production, this should be Redis or a database
progress_storage: Dict[str, Dict[str, Any]] = {}

class SEOAnalysisRequest(BaseModel):
    """Request model for SEO analysis"""
    url: HttpUrl
    mode: Optional[str] = "page"  # "page" or "website"
    enable_progress: Optional[bool] = False  # Enable real-time progress tracking

class SEORecommendation(BaseModel):
    """SEO recommendation model"""
    category: str
    issue: str
    importance: str
    recommendation: str

class Achievement(BaseModel):
    """Achievement model for positive SEO aspects"""
    category: str
    achievement: str
    description: str
    icon: str
    impact: str

class BasicInfo(BaseModel):
    """Basic SEO information model"""
    title: str
    title_length: int
    meta_description: Optional[str]
    meta_description_length: int
    h1_tags: List[str]
    h1_count: int
    canonical_url: Optional[str]
    language: Optional[str]
    has_viewport: bool
    viewport_content: Optional[str]
    meta_robots: Optional[str]

class TechnicalAudit(BaseModel):
    """Technical audit model"""
    is_https: bool
    status_code: int
    response_time: float

class ImagesInfo(BaseModel):
    """Images information model"""
    total: int
    without_alt: int
    without_alt_percentage: float

class LinksInfo(BaseModel):
    """Links information model"""
    total: int
    internal_count: int
    external_count: int
    internal_links: List[str]
    external_links: List[str]

class TopKeyword(BaseModel):
    """Top keyword model"""
    word: str
    count: int

class RankingKeyword(BaseModel):
    """Ranking keyword model"""
    keyword: str
    position: int
    url: str
    title: str
    snippet: str
    exact_match: bool
    domain_match: bool

class KeywordAnalysis(BaseModel):
    """Keyword analysis model"""
    top_positions: List[RankingKeyword]
    good_positions: List[RankingKeyword]
    opportunities: List[RankingKeyword]
    average_position: float
    total_rankings: int
    best_position: Optional[int] = None
    worst_position: Optional[int] = None

class RankingKeywordsData(BaseModel):
    """Ranking keywords data model"""
    total_keywords_found: int
    ranking_keywords: List[RankingKeyword]
    keyword_analysis: KeywordAnalysis
    search_queries_tested: int
    domain: str
    error: Optional[str] = None

class ContentAnalysis(BaseModel):
    """Content analysis model"""
    word_count: int
    reading_time_minutes: int
    headings_structure: Dict[str, List[str]]
    images: ImagesInfo
    links: LinksInfo
    top_keywords: List[TopKeyword]
    ranking_keywords: Optional[RankingKeywordsData] = None

class PreviewData(BaseModel):
    """Preview data model"""
    title: str
    description: str
    url: str
    og_title: str
    og_description: str
    og_image: str
    twitter_title: str
    twitter_description: str
    twitter_image: str

class MetricValue(BaseModel):
    """Core Web Vitals metric value model"""
    value: Optional[float]
    display_value: Optional[str]
    score: float
    rating: str

class CoreWebVitals(BaseModel):
    """Core Web Vitals model"""
    largest_contentful_paint: MetricValue
    first_input_delay: MetricValue
    cumulative_layout_shift: MetricValue
    first_contentful_paint: MetricValue
    speed_index: MetricValue
    time_to_interactive: MetricValue

class LighthouseScores(BaseModel):
    """Lighthouse scores model"""
    performance: float
    accessibility: float
    best_practices: float
    seo: float

class PerformanceMetrics(BaseModel):
    """Performance metrics model"""
    core_web_vitals: CoreWebVitals
    lighthouse_scores: LighthouseScores
    overall_performance_score: float

class SEOAnalysisResponse(BaseModel):
    """Response model for SEO analysis"""
    status: str
    url: str
    basic_info: BasicInfo
    open_graph: Dict[str, Optional[str]]
    twitter_card: Dict[str, Optional[str]]
    technical_audit: TechnicalAudit
    content_analysis: ContentAnalysis
    performance_metrics: PerformanceMetrics
    seo_checks: Dict[str, bool]
    preview_data: PreviewData
    recommendations: List[SEORecommendation]
    achievements: List[Achievement]
    ai_enhanced: bool
    processing_time: float
    error_message: Optional[str] = None

class SEOAnalysisErrorResponse(BaseModel):
    """Error response model for SEO analysis"""
    status: str
    error_message: str
    url: str
    ai_enhanced: bool

@router.post("/analyze")
async def analyze_seo(request: SEOAnalysisRequest, http_request: Request):
    """
    Perform comprehensive SEO analysis of a website.
    
    This endpoint provides detailed SEO analysis including:
    - Technical SEO audit (meta tags, headers, HTTPS, etc.)
    - Content analysis (word count, headings structure, keywords)
    - Images and links analysis
    - AI-powered recommendations using Gemini
    - Social media preview generation
    - Overall SEO score calculation
    """
    start_time = time.time()
    request_id = http_request.headers.get("X-Request-ID", f"seo_req_{int(time.time()*1000)}")
    
    logger.info(f"SEO analysis request received. RequestID: {request_id}, URL: {request.url}")
    
    try:
        # Perform SEO analysis
        result = await seo_analyzer.analyze_website(str(request.url))

        if result["status"] == "error":
            logger.error(f"SEO analysis failed. RequestID: {request_id}, Error: {result.get('error_message')}")

            # Return a structured error response with 200 status but error content
            return {
                "status": "error",
                "error_message": result.get("error_message", "SEO analysis failed"),
                "url": str(request.url),
                "ai_enhanced": result.get("ai_enhanced", False)
            }

        processing_time = time.time() - start_time

        logger.info(f"SEO analysis completed successfully. RequestID: {request_id}, ProcessingTime: {processing_time:.2f}s")

        # Safely extract data with defaults to prevent Pydantic validation errors
        def safe_get(data, key, default=None):
            """Safely get nested dictionary values with defaults"""
            if isinstance(data, dict):
                return data.get(key, default)
            return default

        # Ensure all required fields have proper defaults
        basic_info_data = safe_get(result, "basic_info", {})
        content_analysis_data = safe_get(result, "content_analysis", {})
        performance_metrics_data = safe_get(result, "performance_metrics", {})

        # Convert result to response model with safe extraction
        return SEOAnalysisResponse(
            status=result.get("status", "success"),
            url=result.get("url", str(request.url)),
            basic_info=BasicInfo(
                title=safe_get(basic_info_data, "title", "Sin título"),
                title_length=safe_get(basic_info_data, "title_length", 0),
                meta_description=safe_get(basic_info_data, "meta_description"),
                meta_description_length=safe_get(basic_info_data, "meta_description_length", 0),
                h1_tags=safe_get(basic_info_data, "h1_tags", []),
                h1_count=safe_get(basic_info_data, "h1_count", 0),
                canonical_url=safe_get(basic_info_data, "canonical_url"),
                language=safe_get(basic_info_data, "language"),
                has_viewport=safe_get(basic_info_data, "has_viewport", False),
                viewport_content=safe_get(basic_info_data, "viewport_content"),
                meta_robots=safe_get(basic_info_data, "meta_robots")
            ),
            open_graph=safe_get(result, "open_graph", {}),
            twitter_card=safe_get(result, "twitter_card", {}),
            technical_audit=TechnicalAudit(
                is_https=safe_get(result.get("technical_audit", {}), "is_https", False),
                status_code=safe_get(result.get("technical_audit", {}), "status_code", 0),
                response_time=safe_get(result.get("technical_audit", {}), "response_time", 0.0)
            ),
            content_analysis=ContentAnalysis(
                word_count=safe_get(content_analysis_data, "word_count", 0),
                reading_time_minutes=safe_get(content_analysis_data, "reading_time_minutes", 0),
                headings_structure=safe_get(content_analysis_data, "headings_structure", {}),
                images=ImagesInfo(
                    total=safe_get(content_analysis_data.get("images", {}), "total", 0),
                    without_alt=safe_get(content_analysis_data.get("images", {}), "without_alt", 0),
                    without_alt_percentage=safe_get(content_analysis_data.get("images", {}), "without_alt_percentage", 0.0)
                ),
                links=LinksInfo(
                    total=safe_get(content_analysis_data.get("links", {}), "total", 0),
                    internal_count=safe_get(content_analysis_data.get("links", {}), "internal_count", 0),
                    external_count=safe_get(content_analysis_data.get("links", {}), "external_count", 0),
                    internal_links=safe_get(content_analysis_data.get("links", {}), "internal_links", []),
                    external_links=safe_get(content_analysis_data.get("links", {}), "external_links", [])
                ),
                top_keywords=[TopKeyword(
                    word=kw.get("word", ""),
                    count=kw.get("count", 0)
                ) for kw in safe_get(content_analysis_data, "top_keywords", [])],
                ranking_keywords=_build_ranking_keywords_data(content_analysis_data.get("ranking_keywords")) if content_analysis_data.get("ranking_keywords") else None
            ),
            performance_metrics=_build_performance_metrics(performance_metrics_data),
            seo_checks=safe_get(result, "seo_checks", {}),
            preview_data=_build_preview_data(result.get("preview_data", {})),
            recommendations=[SEORecommendation(
                category=rec.get("category", "General"),
                issue=rec.get("issue", ""),
                importance=rec.get("importance", "medium"),
                recommendation=rec.get("recommendation", "")
            ) for rec in safe_get(result, "recommendations", [])],
            achievements=[Achievement(
                category=ach.get("category", "General"),
                achievement=ach.get("achievement", ""),
                description=ach.get("description", ""),
                icon=ach.get("icon", "✓"),
                impact=ach.get("impact", "positive")
            ) for ach in safe_get(result, "achievements", [])],
            ai_enhanced=result.get("ai_enhanced", False),
            processing_time=processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Unexpected error in SEO analysis. RequestID: {request_id}, Error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during SEO analysis: {str(e)}"
        )

def _build_ranking_keywords_data(ranking_data: Dict[str, Any]) -> Optional[RankingKeywordsData]:
    """Safely build ranking keywords data with defaults"""
    if not ranking_data:
        return None

    try:
        return RankingKeywordsData(
            total_keywords_found=ranking_data.get("total_keywords_found", 0),
            ranking_keywords=[RankingKeyword(
                keyword=kw.get("keyword", ""),
                position=kw.get("position", 0),
                url=kw.get("url", ""),
                title=kw.get("title", ""),
                snippet=kw.get("snippet", ""),
                exact_match=kw.get("exact_match", False),
                domain_match=kw.get("domain_match", False)
            ) for kw in ranking_data.get("ranking_keywords", [])],
            keyword_analysis=KeywordAnalysis(
                top_positions=ranking_data.get("keyword_analysis", {}).get("top_positions", []),
                good_positions=ranking_data.get("keyword_analysis", {}).get("good_positions", []),
                opportunities=ranking_data.get("keyword_analysis", {}).get("opportunities", []),
                average_position=ranking_data.get("keyword_analysis", {}).get("average_position", 0.0),
                total_rankings=ranking_data.get("keyword_analysis", {}).get("total_rankings", 0),
                best_position=ranking_data.get("keyword_analysis", {}).get("best_position"),
                worst_position=ranking_data.get("keyword_analysis", {}).get("worst_position")
            ),
            search_queries_tested=ranking_data.get("search_queries_tested", 0),
            domain=ranking_data.get("domain", ""),
            error=ranking_data.get("error")
        )
    except Exception as e:
        logger.error(f"Error building ranking keywords data: {str(e)}")
        return None

def _build_performance_metrics(performance_data: Dict[str, Any]) -> PerformanceMetrics:
    """Safely build performance metrics with defaults"""
    try:
        core_web_vitals_data = performance_data.get("core_web_vitals", {})
        lighthouse_scores_data = performance_data.get("lighthouse_scores", {})

        return PerformanceMetrics(
            core_web_vitals=CoreWebVitals(
                largest_contentful_paint=MetricValue(
                    value=core_web_vitals_data.get("largest_contentful_paint", {}).get("value"),
                    display_value=core_web_vitals_data.get("largest_contentful_paint", {}).get("display_value", "No disponible"),
                    score=core_web_vitals_data.get("largest_contentful_paint", {}).get("score", 0.0),
                    rating=core_web_vitals_data.get("largest_contentful_paint", {}).get("rating", "unknown")
                ),
                first_input_delay=MetricValue(
                    value=core_web_vitals_data.get("first_input_delay", {}).get("value"),
                    display_value=core_web_vitals_data.get("first_input_delay", {}).get("display_value", "No disponible"),
                    score=core_web_vitals_data.get("first_input_delay", {}).get("score", 0.0),
                    rating=core_web_vitals_data.get("first_input_delay", {}).get("rating", "unknown")
                ),
                cumulative_layout_shift=MetricValue(
                    value=core_web_vitals_data.get("cumulative_layout_shift", {}).get("value"),
                    display_value=core_web_vitals_data.get("cumulative_layout_shift", {}).get("display_value", "No disponible"),
                    score=core_web_vitals_data.get("cumulative_layout_shift", {}).get("score", 0.0),
                    rating=core_web_vitals_data.get("cumulative_layout_shift", {}).get("rating", "unknown")
                ),
                first_contentful_paint=MetricValue(
                    value=core_web_vitals_data.get("first_contentful_paint", {}).get("value"),
                    display_value=core_web_vitals_data.get("first_contentful_paint", {}).get("display_value", "No disponible"),
                    score=core_web_vitals_data.get("first_contentful_paint", {}).get("score", 0.0),
                    rating=core_web_vitals_data.get("first_contentful_paint", {}).get("rating", "unknown")
                ),
                speed_index=MetricValue(
                    value=core_web_vitals_data.get("speed_index", {}).get("value"),
                    display_value=core_web_vitals_data.get("speed_index", {}).get("display_value", "No disponible"),
                    score=core_web_vitals_data.get("speed_index", {}).get("score", 0.0),
                    rating=core_web_vitals_data.get("speed_index", {}).get("rating", "unknown")
                ),
                time_to_interactive=MetricValue(
                    value=core_web_vitals_data.get("time_to_interactive", {}).get("value"),
                    display_value=core_web_vitals_data.get("time_to_interactive", {}).get("display_value", "No disponible"),
                    score=core_web_vitals_data.get("time_to_interactive", {}).get("score", 0.0),
                    rating=core_web_vitals_data.get("time_to_interactive", {}).get("rating", "unknown")
                )
            ),
            lighthouse_scores=LighthouseScores(
                performance=lighthouse_scores_data.get("performance", 0.0),
                accessibility=lighthouse_scores_data.get("accessibility", 0.0),
                best_practices=lighthouse_scores_data.get("best_practices", 0.0),
                seo=lighthouse_scores_data.get("seo", 0.0)
            ),
            overall_performance_score=performance_data.get("overall_performance_score", 0.0)
        )
    except Exception as e:
        logger.error(f"Error building performance metrics: {str(e)}")
        # Return default performance metrics
        return PerformanceMetrics(
            core_web_vitals=CoreWebVitals(
                largest_contentful_paint=MetricValue(value=None, display_value="No disponible", score=0.0, rating="unknown"),
                first_input_delay=MetricValue(value=None, display_value="No disponible", score=0.0, rating="unknown"),
                cumulative_layout_shift=MetricValue(value=None, display_value="No disponible", score=0.0, rating="unknown"),
                first_contentful_paint=MetricValue(value=None, display_value="No disponible", score=0.0, rating="unknown"),
                speed_index=MetricValue(value=None, display_value="No disponible", score=0.0, rating="unknown"),
                time_to_interactive=MetricValue(value=None, display_value="No disponible", score=0.0, rating="unknown")
            ),
            lighthouse_scores=LighthouseScores(performance=0.0, accessibility=0.0, best_practices=0.0, seo=0.0),
            overall_performance_score=0.0
        )

def _build_preview_data(preview_data: Dict[str, Any]) -> PreviewData:
    """Safely build preview data with defaults"""
    return PreviewData(
        title=preview_data.get("title", "Sin título"),
        description=preview_data.get("description", "Sin descripción"),
        url=preview_data.get("url", ""),
        og_title=preview_data.get("og_title", ""),
        og_description=preview_data.get("og_description", ""),
        og_image=preview_data.get("og_image", ""),
        twitter_title=preview_data.get("twitter_title", ""),
        twitter_description=preview_data.get("twitter_description", ""),
        twitter_image=preview_data.get("twitter_image", "")
    )

@router.post("/analyze-website")
async def analyze_website_complete(request: SEOAnalysisRequest, http_request: Request):
    """
    Perform comprehensive SEO analysis of an entire website.

    This endpoint provides detailed SEO analysis including:
    - Multi-page crawling and analysis
    - Site-wide technical SEO audit
    - Content analysis across multiple pages
    - Site architecture and internal linking
    - AI-powered recommendations for the entire site
    - Overall site SEO score calculation
    """
    start_time = time.time()
    request_id = http_request.headers.get("X-Request-ID", f"seo_website_req_{int(time.time()*1000)}")

    logger.info(f"Website SEO analysis request received. RequestID: {request_id}, URL: {request.url}, Progress: {request.enable_progress}")

    try:
        logger.info(f"Starting persistent SEO analysis for URL: {request.url}, Mode: {request.mode}")

        # Use persistent service for long-running analysis
        result = await persistent_seo_service.start_analysis(
            url=str(request.url),
            mode=request.mode or "website",
            user_id=None,  # TODO: Get from auth context
            enable_progress=request.enable_progress or True
        )

        logger.info(f"Persistent SEO analysis started successfully: {result.get('analysis_id')}")
        return result

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Unexpected error starting website SEO analysis. RequestID: {request_id}, Error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error al iniciar análisis SEO: {str(e)}"
        )

@router.get("/test")
async def test_seo_service():
    """Test endpoint to verify SEO analysis service connectivity"""
    try:
        # Test with a simple, reliable website
        test_url = "https://example.com"
        result = await seo_analyzer.analyze_website(test_url)

        return {
            "status": "success",
            "message": "SEO analysis service test successful",
            "test_url": test_url,
            "analysis_completed": result["status"] == "success",
            "ai_enhanced": result.get("ai_enhanced", False)
        }

    except Exception as e:
        logger.error(f"SEO service test failed: {str(e)}")
        return {
            "status": "error",
            "message": f"SEO analysis service test failed: {str(e)}"
        }


@router.get("/debug/persistent")
async def debug_persistent_system():
    """Debug endpoint to test persistent SEO system"""
    try:
        from app.db.session import get_db
        from app.db.models import SEOAnalysis

        # Test database connection
        db = next(get_db())
        try:
            count = db.query(SEOAnalysis).count()
            db_status = f"Connected - {count} analyses in database"
        except Exception as e:
            db_status = f"Error: {str(e)}"
        finally:
            db.close()

        # Test persistent service
        try:
            analyses = persistent_seo_service.get_user_analyses(limit=5)
            service_status = f"Working - Retrieved {len(analyses)} analyses"
        except Exception as e:
            service_status = f"Error: {str(e)}"

        return {
            "status": "success",
            "database": db_status,
            "persistent_service": service_status,
            "active_analyses": len(persistent_seo_service.active_analyses),
            "message": "Persistent SEO system debug complete"
        }

    except Exception as e:
        logger.error(f"Debug test failed: {str(e)}")
        return {
            "status": "error",
            "message": f"Debug test failed: {str(e)}"
        }

# Legacy function removed - now using persistent_seo_service

@router.get("/progress/{analysis_id}")
async def get_analysis_progress(analysis_id: str):
    """
    Get the progress of a website analysis from persistent storage
    """
    progress_data = persistent_seo_service.get_analysis_progress(analysis_id)

    if not progress_data:
        raise HTTPException(
            status_code=404,
            detail=f"Analysis {analysis_id} not found"
        )

    return progress_data


@router.get("/analyses")
async def get_user_analyses(
    status: Optional[str] = None,
    limit: int = 50,
    user_id: Optional[str] = None  # TODO: Get from auth context
):
    """
    Get list of user's SEO analyses
    """
    analyses = persistent_seo_service.get_user_analyses(
        user_id=user_id,
        limit=limit,
        status_filter=status
    )

    return {
        "analyses": analyses,
        "total": len(analyses)
    }


@router.delete("/analyses/{analysis_id}")
async def cancel_analysis(analysis_id: str):
    """
    Cancel a running analysis
    """
    success = await persistent_seo_service.cancel_analysis(analysis_id)

    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"Analysis {analysis_id} not found or cannot be cancelled"
        )

    return {
        "status": "cancelled",
        "analysis_id": analysis_id,
        "message": "Análisis cancelado exitosamente"
    }
