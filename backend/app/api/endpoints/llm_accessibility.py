"""
LLM Accessibility API Endpoints for Emma Studio
Provides robots.txt, sitemap.xml, llms.txt and other crawler optimization features
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, Request, Query
from fastapi.responses import PlainTextResponse, Response
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.services.llm_accessibility_service import llm_accessibility_service

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/robots.txt", response_class=PlainTextResponse)
async def get_robots_txt(
    allow_all: bool = Query(True, description="Allow all bots by default"),
    request: Request = None
):
    """
    Generate and serve robots.txt optimized for LLM bots.
    
    This endpoint generates a robots.txt file that specifically allows
    LLM bots like GPTBot, ClaudeBot, and OAI-SearchBot while maintaining
    proper access controls for other crawlers.
    """
    try:
        # Log the request for monitoring
        user_agent = request.headers.get("user-agent", "") if request else ""
        logger.info(f"robots.txt requested by: {user_agent}")
        
        robots_content = llm_accessibility_service.generate_robots_txt(
            allow_all_bots=allow_all
        )
        
        return PlainTextResponse(
            content=robots_content,
            media_type="text/plain",
            headers={
                "Cache-Control": "public, max-age=86400",  # Cache for 24 hours
                "X-Robots-Tag": "noindex"  # Don't index the robots.txt itself
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to generate robots.txt: {e}")
        # Return basic robots.txt as fallback
        return PlainTextResponse(
            content="User-agent: *\nAllow: /",
            media_type="text/plain"
        )

@router.get("/sitemap.xml")
async def get_sitemap_xml(
    db: Session = Depends(get_db),
    include_dynamic: bool = Query(True, description="Include dynamic content"),
    request: Request = None
):
    """
    Generate and serve dynamic sitemap.xml.
    
    This endpoint generates a sitemap.xml file that includes both static
    pages and dynamic content from the database, optimized for LLM discovery.
    """
    try:
        # Log the request for monitoring
        user_agent = request.headers.get("user-agent", "") if request else ""
        logger.info(f"sitemap.xml requested by: {user_agent}")
        
        sitemap_content = llm_accessibility_service.generate_sitemap_xml(
            db=db,
            include_dynamic=include_dynamic
        )
        
        return Response(
            content=sitemap_content,
            media_type="application/xml",
            headers={
                "Cache-Control": "public, max-age=3600",  # Cache for 1 hour
                "X-Robots-Tag": "noindex"
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to generate sitemap.xml: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate sitemap: {str(e)}"
        )

@router.get("/llms.txt", response_class=PlainTextResponse)
async def get_llms_txt(request: Request = None):
    """
    Generate and serve llms.txt for LLM bot instructions.
    
    This endpoint serves a llms.txt file that provides specific instructions
    and preferences for Large Language Model bots crawling the site.
    """
    try:
        # Log the request for monitoring
        user_agent = request.headers.get("user-agent", "") if request else ""
        logger.info(f"llms.txt requested by: {user_agent}")
        
        llms_content = llm_accessibility_service.generate_llms_txt()
        
        return PlainTextResponse(
            content=llms_content,
            media_type="text/plain",
            headers={
                "Cache-Control": "public, max-age=86400",  # Cache for 24 hours
                "X-Robots-Tag": "noindex"
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to generate llms.txt: {e}")
        return PlainTextResponse(
            content="# Emma Studio - LLMs.txt\nSite-name: Emma Studio",
            media_type="text/plain"
        )

@router.get("/ai.txt", response_class=PlainTextResponse)
async def get_ai_txt(request: Request = None):
    """
    Generate and serve ai.txt for AI bot instructions.
    
    This endpoint serves an ai.txt file that provides AI training permissions
    and content licensing information for AI bots.
    """
    try:
        # Log the request for monitoring
        user_agent = request.headers.get("user-agent", "") if request else ""
        logger.info(f"ai.txt requested by: {user_agent}")
        
        ai_content = llm_accessibility_service.generate_ai_txt()
        
        return PlainTextResponse(
            content=ai_content,
            media_type="text/plain",
            headers={
                "Cache-Control": "public, max-age=86400",  # Cache for 24 hours
                "X-Robots-Tag": "noindex"
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to generate ai.txt: {e}")
        return PlainTextResponse(
            content="# Emma Studio - AI.txt\nContact: <EMAIL>",
            media_type="text/plain"
        )

@router.get("/url-structure")
async def get_url_structure():
    """
    Get clean URL structure for LLM-friendly navigation.
    
    This endpoint returns the organized URL structure of the site
    to help LLM bots understand the content organization.
    """
    try:
        url_structure = llm_accessibility_service.get_clean_url_structure()
        
        return {
            "url_structure": url_structure,
            "total_categories": len(url_structure),
            "total_urls": sum(len(urls) for urls in url_structure.values()),
            "description": "Clean URL structure optimized for LLM bot navigation"
        }
        
    except Exception as e:
        logger.error(f"Failed to get URL structure: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get URL structure: {str(e)}"
        )

@router.get("/validate-bot")
async def validate_bot_access(request: Request):
    """
    Validate bot access based on user agent.
    
    This endpoint analyzes the user agent and determines
    the appropriate access level for the requesting bot.
    """
    try:
        user_agent = request.headers.get("user-agent", "")
        
        if not user_agent:
            raise HTTPException(
                status_code=400,
                detail="User-Agent header is required"
            )
        
        validation_result = llm_accessibility_service.validate_bot_access(user_agent)
        
        # Add additional context
        validation_result.update({
            "timestamp": str(datetime.now()),
            "client_ip": request.client.host if request.client else "unknown",
            "recommendations": []
        })
        
        # Add recommendations based on bot type
        if validation_result["is_llm_bot"]:
            validation_result["recommendations"].extend([
                "Use /api/v1/search/search for semantic content discovery",
                "Access /api/v1/structured-data/ for Schema.org markup",
                "Check /llms.txt for crawling preferences",
                "Respect rate limits: 1 request per second"
            ])
        elif validation_result["is_allowed"]:
            validation_result["recommendations"].extend([
                "Follow robots.txt guidelines",
                "Use sitemap.xml for content discovery",
                "Respect crawl-delay settings"
            ])
        
        return validation_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to validate bot access: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to validate bot access: {str(e)}"
        )

@router.get("/crawler-stats")
async def get_crawler_stats(
    days: int = Query(7, description="Number of days to analyze", ge=1, le=30)
):
    """
    Get crawler statistics and bot activity.
    
    This endpoint provides statistics about bot and crawler activity
    for monitoring and optimization purposes.
    """
    try:
        # This would typically query access logs or analytics data
        # For now, return mock data structure
        
        stats = {
            "period_days": days,
            "total_requests": 0,  # Would be populated from logs
            "bot_requests": 0,
            "llm_bot_requests": 0,
            "top_bots": [],  # Would be populated from logs
            "top_endpoints": [],
            "error_rate": 0.0,
            "average_response_time": 0.0,
            "recommendations": [
                "Monitor for unusual bot activity",
                "Ensure robots.txt is accessible",
                "Keep sitemap.xml updated",
                "Monitor API rate limits"
            ]
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get crawler stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get crawler stats: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """
    Health check for LLM accessibility service.
    
    This endpoint provides health status and configuration
    information for the LLM accessibility features.
    """
    try:
        return {
            "service": "LLM Accessibility",
            "status": "healthy",
            "features": {
                "robots_txt": True,
                "sitemap_xml": True,
                "llms_txt": True,
                "ai_txt": True,
                "bot_validation": True,
                "url_structure": True
            },
            "supported_bots": llm_accessibility_service.allowed_bots,
            "base_url": llm_accessibility_service.base_url,
            "accessibility_files": [
                "/api/v1/llm-access/robots.txt",
                "/api/v1/llm-access/sitemap.xml", 
                "/api/v1/llm-access/llms.txt",
                "/api/v1/llm-access/ai.txt"
            ]
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Health check failed: {str(e)}"
        )

@router.get("/status")
async def get_accessibility_status():
    """
    Get detailed status of LLM accessibility features.
    
    This endpoint provides comprehensive status information
    about all LLM accessibility features and their configuration.
    """
    try:
        return {
            "service": "LLM Accessibility API",
            "version": "1.0.0",
            "status": "operational",
            "configuration": {
                "base_url": llm_accessibility_service.base_url,
                "allowed_bots_count": len(llm_accessibility_service.allowed_bots),
                "priority_paths_count": len(llm_accessibility_service.priority_paths)
            },
            "endpoints": {
                "robots_txt": "/api/v1/llm-access/robots.txt",
                "sitemap_xml": "/api/v1/llm-access/sitemap.xml",
                "llms_txt": "/api/v1/llm-access/llms.txt",
                "ai_txt": "/api/v1/llm-access/ai.txt",
                "url_structure": "/api/v1/llm-access/url-structure",
                "bot_validation": "/api/v1/llm-access/validate-bot"
            },
            "features": {
                "dynamic_sitemap": True,
                "llm_bot_optimization": True,
                "clean_url_structure": True,
                "bot_access_validation": True,
                "crawler_monitoring": True
            },
            "supported_formats": ["text/plain", "application/xml", "application/json"]
        }
        
    except Exception as e:
        logger.error(f"Failed to get accessibility status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get status: {str(e)}"
        )
