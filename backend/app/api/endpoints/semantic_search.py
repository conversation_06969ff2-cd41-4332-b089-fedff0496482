"""
Semantic Search API Endpoints for Emma Studio
Provides semantic search capabilities for content discovery and similarity matching
"""

import logging
import time
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.session import get_db
from app.services.semantic_search_service import semantic_search_service

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic models for request/response
class SemanticSearchRequest(BaseModel):
    """Request model for semantic search."""
    query: str = Field(..., description="Search query text", min_length=1, max_length=1000)
    top_k: int = Field(10, description="Number of results to return", ge=1, le=50)
    content_type: Optional[str] = Field(None, description="Filter by content type")

class SemanticSearchResult(BaseModel):
    """Individual search result model."""
    content_id: str = Field(..., description="Unique content identifier")
    title: str = Field(..., description="Content title")
    content_type: str = Field(..., description="Type of content")
    content_preview: str = Field(..., description="Preview of content")
    similarity_score: float = Field(..., description="Similarity score (0-1)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    added_at: Optional[str] = Field(None, description="When content was added to index")

class SemanticSearchResponse(BaseModel):
    """Response model for semantic search."""
    query: str = Field(..., description="Original search query")
    results: List[SemanticSearchResult] = Field(..., description="Search results")
    total_results: int = Field(..., description="Total number of results")
    processing_time_ms: float = Field(..., description="Processing time in milliseconds")

class AddContentRequest(BaseModel):
    """Request model for adding content to search index."""
    content_id: str = Field(..., description="Unique content identifier")
    title: str = Field(..., description="Content title", min_length=1, max_length=500)
    content: str = Field(..., description="Content text", min_length=1)
    content_type: str = Field("article", description="Type of content")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class SimilarContentRequest(BaseModel):
    """Request model for finding similar content."""
    content_id: str = Field(..., description="Content ID to find similar content for")
    top_k: int = Field(5, description="Number of similar results to return", ge=1, le=20)

@router.post("/search", response_model=SemanticSearchResponse)
async def semantic_search(request: SemanticSearchRequest):
    """
    Perform semantic search across indexed content.
    
    This endpoint uses Sentence-BERT embeddings and FAISS vector search
    to find semantically similar content based on meaning rather than keywords.
    """
    try:
        import time
        start_time = time.time()
        
        # Ensure service is initialized
        if not semantic_search_service.model:
            await semantic_search_service.initialize()
        
        # Perform search
        results = await semantic_search_service.search(
            query=request.query,
            top_k=request.top_k,
            content_type=request.content_type
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        # Format response
        search_results = [
            SemanticSearchResult(**result) for result in results
        ]
        
        return SemanticSearchResponse(
            query=request.query,
            results=search_results,
            total_results=len(search_results),
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        logger.error(f"Semantic search failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Semantic search failed: {str(e)}"
        )

@router.post("/add-content")
async def add_content_to_index(request: AddContentRequest):
    """
    Add content to the semantic search index.
    
    This endpoint allows adding new content to the searchable index
    for future semantic search queries.
    """
    try:
        # Ensure service is initialized
        if not semantic_search_service.model:
            await semantic_search_service.initialize()
        
        # Add content to index
        success = await semantic_search_service.add_content_to_index(
            content_id=request.content_id,
            title=request.title,
            content=request.content,
            content_type=request.content_type,
            metadata=request.metadata
        )
        
        if success:
            return JSONResponse(
                status_code=200,
                content={
                    "message": "Content added to semantic search index successfully",
                    "content_id": request.content_id
                }
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to add content to search index"
            )
            
    except Exception as e:
        logger.error(f"Failed to add content to index: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to add content: {str(e)}"
        )

@router.post("/similar", response_model=List[SemanticSearchResult])
async def find_similar_content(request: SimilarContentRequest):
    """
    Find content similar to a given content ID.
    
    This endpoint finds semantically similar content to a specific piece
    of content already in the index.
    """
    try:
        # Ensure service is initialized
        if not semantic_search_service.model:
            await semantic_search_service.initialize()
        
        # Find similar content
        results = await semantic_search_service.get_similar_content(
            content_id=request.content_id,
            top_k=request.top_k
        )
        
        # Format response
        return [SemanticSearchResult(**result) for result in results]
        
    except Exception as e:
        logger.error(f"Failed to find similar content: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to find similar content: {str(e)}"
        )

@router.post("/rebuild-index")
async def rebuild_search_index(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Rebuild the semantic search index from database content.
    
    This endpoint rebuilds the entire search index from existing
    database content. This is useful for initial setup or after
    major content changes.
    """
    try:
        # Add rebuild task to background
        background_tasks.add_task(
            semantic_search_service.rebuild_index_from_database,
            db
        )
        
        return JSONResponse(
            status_code=202,
            content={
                "message": "Index rebuild started in background",
                "status": "processing"
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to start index rebuild: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start index rebuild: {str(e)}"
        )

@router.get("/status")
async def get_search_status():
    """
    Get the current status of the semantic search service.
    
    Returns information about the search index and service health.
    """
    try:
        # Check if service is initialized
        is_initialized = semantic_search_service.model is not None
        
        # Get index stats
        total_vectors = 0
        if semantic_search_service.index:
            total_vectors = semantic_search_service.index.ntotal
        
        return JSONResponse(
            status_code=200,
            content={
                "service": "Semantic Search API",
                "version": "1.0.0",
                "status": "healthy" if is_initialized else "initializing",
                "model": semantic_search_service.model_name,
                "total_indexed_content": total_vectors,
                "embedding_dimension": semantic_search_service.embedding_dim,
                "features": {
                    "semantic_search": True,
                    "similarity_matching": True,
                    "content_indexing": True,
                    "background_rebuild": True
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get search status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get status: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """Health check endpoint for semantic search service."""
    try:
        # Basic health check
        if semantic_search_service.model is None:
            await semantic_search_service.initialize()
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "healthy",
                "service": "semantic_search",
                "timestamp": str(time.time())
            }
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "semantic_search",
                "error": str(e)
            }
        )
