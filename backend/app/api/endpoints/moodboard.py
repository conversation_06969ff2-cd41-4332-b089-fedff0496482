"""
API endpoints for interactive moodboard management with Supabase integration.
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional, List, Dict, Any

from app.core.auth import get_current_user_from_token
from app.core.supabase import get_supabase_service, SupabaseService
from app.schemas.moodboard import (
    MoodboardCreateRequest,
    MoodboardUpdateRequest,
    MoodboardResponse,
    MoodboardListResponse,
    MoodboardHistoryCreateRequest,
    MoodboardHistoryResponse,
    MoodboardOperationResponse,
    MoodboardStatsResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/create", response_model=MoodboardOperationResponse)
async def create_moodboard(
    request: MoodboardCreateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> MoodboardOperationResponse:
    """
    Create a new interactive moodboard.
    
    Creates a new moodboard with the provided data and associates it with the current user.
    """
    try:
        logger.info(f"Creating new moodboard for user: {current_user['user_id']}")
        
        # Prepare moodboard data
        moodboard_data = {
            "title": request.title,
            "description": request.description,
            "tldraw_data": request.tldraw_data,
            "canvas_snapshot": request.canvas_snapshot,
            "tags": request.tags,
            "is_public": request.is_public,
            "is_favorite": request.is_favorite,
            "collaboration_enabled": request.collaboration_enabled,
            "shared_with": request.shared_with,
            "notes": request.notes
        }
        
        # Save to database
        result = await supabase_service.save_moodboard(
            user_id=current_user["user_id"],
            moodboard_data=moodboard_data,
            user_jwt_token=current_user.get("jwt_token")
        )

        if result:
            # Create initial history entry
            if request.tldraw_data:
                try:
                    await supabase_service.save_moodboard_history(
                        moodboard_id=result["id"],
                        user_id=current_user["user_id"],
                        change_type="create",
                        tldraw_data_snapshot=request.tldraw_data,
                        change_description="Initial moodboard creation",
                        is_auto_save=False
                    )
                except Exception as history_error:
                    logger.warning(f"Failed to create initial history entry: {history_error}")
                    # Don't fail the entire operation if history creation fails

            logger.info(f"Successfully created moodboard {result['id']} for user {current_user['user_id']}")
            return MoodboardOperationResponse(
                success=True,
                message="Moodboard created successfully",
                data=result
            )
        else:
            logger.error(f"Failed to create moodboard for user {current_user['user_id']}: No result returned from save_moodboard")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create moodboard: Database operation returned no result"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating moodboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/list", response_model=MoodboardListResponse)
async def list_moodboards(
    page: int = Query(default=1, ge=1, description="Page number"),
    limit: int = Query(default=20, ge=1, le=100, description="Items per page"),
    status: str = Query(default="active", description="Filter by status"),
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> MoodboardListResponse:
    """
    List all moodboards for the current user.
    
    Returns a paginated list of moodboards owned by the current user.
    """
    try:
        logger.info(f"Listing moodboards for user: {current_user['user_id']}")
        
        offset = (page - 1) * limit
        
        # Get moodboards from database
        moodboards = await supabase_service.get_user_moodboards(
            user_id=current_user["user_id"],
            limit=limit,
            offset=offset,
            status=status,
            user_jwt_token=current_user.get("jwt_token")
        )
        
        # Convert to response format
        moodboard_responses = [
            MoodboardResponse(**moodboard) for moodboard in moodboards
        ]
        
        logger.info(f"Retrieved {len(moodboards)} moodboards for user {current_user['user_id']}")
        
        return MoodboardListResponse(
            moodboards=moodboard_responses,
            total_count=len(moodboards),  # TODO: Implement proper count query
            page=page,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"Error listing moodboards: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/{moodboard_id}", response_model=MoodboardOperationResponse)
async def get_moodboard(
    moodboard_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> MoodboardOperationResponse:
    """
    Get a specific moodboard by ID.
    
    Returns the moodboard data if the user has access to it.
    """
    try:
        logger.info(f"Getting moodboard {moodboard_id} for user: {current_user['user_id']}")
        
        # Get moodboard from database
        moodboard = await supabase_service.get_moodboard_by_id(
            moodboard_id=moodboard_id,
            user_id=current_user["user_id"],
            user_jwt_token=current_user.get("jwt_token")
        )
        
        if moodboard:
            logger.info(f"Retrieved moodboard {moodboard_id} for user {current_user['user_id']}")
            return MoodboardOperationResponse(
                success=True,
                message="Moodboard retrieved successfully",
                data=moodboard
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Mood board not found. This mood board either doesn't exist or you don't have permission to access it."
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting moodboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.put("/{moodboard_id}", response_model=MoodboardOperationResponse)
async def update_moodboard(
    moodboard_id: str,
    request: MoodboardUpdateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> MoodboardOperationResponse:
    """
    Update an existing moodboard.
    
    Updates the moodboard with the provided data if the user has access to it.
    """
    try:
        logger.info(f"Updating moodboard {moodboard_id} for user: {current_user['user_id']}")
        
        # Prepare update data (only include non-None values)
        update_data = {}
        for field, value in request.dict(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value
        
        # Update in database
        result = await supabase_service.update_moodboard(
            moodboard_id=moodboard_id,
            user_id=current_user["user_id"],
            moodboard_data=update_data,
            user_jwt_token=current_user.get("jwt_token")
        )
        
        if result:
            # Create history entry if tldraw_data was updated
            if "tldraw_data" in update_data and update_data["tldraw_data"]:
                await supabase_service.save_moodboard_history(
                    moodboard_id=moodboard_id,
                    user_id=current_user["user_id"],
                    change_type="update",
                    tldraw_data_snapshot=update_data["tldraw_data"],
                    change_description="Moodboard updated",
                    is_auto_save=True
                )
            
            logger.info(f"Successfully updated moodboard {moodboard_id} for user {current_user['user_id']}")
            return MoodboardOperationResponse(
                success=True,
                message="Moodboard updated successfully",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Moodboard not found or access denied"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating moodboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.delete("/{moodboard_id}", response_model=MoodboardOperationResponse)
async def delete_moodboard(
    moodboard_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> MoodboardOperationResponse:
    """
    Delete a moodboard (soft delete).

    Marks the moodboard as deleted if the user has access to it.
    """
    try:
        logger.info(f"Deleting moodboard {moodboard_id} for user: {current_user['user_id']}")

        # Delete from database (soft delete)
        success = await supabase_service.delete_moodboard(
            moodboard_id=moodboard_id,
            user_id=current_user["user_id"],
            user_jwt_token=current_user.get("jwt_token")
        )

        if success:
            logger.info(f"Successfully deleted moodboard {moodboard_id} for user {current_user['user_id']}")
            return MoodboardOperationResponse(
                success=True,
                message="Moodboard deleted successfully"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Moodboard not found or access denied"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting moodboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/{moodboard_id}/history", response_model=MoodboardOperationResponse)
async def create_moodboard_history(
    moodboard_id: str,
    request: MoodboardHistoryCreateRequest,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> MoodboardOperationResponse:
    """
    Create a history entry for a moodboard.

    Saves a snapshot of the moodboard state for version control.
    """
    try:
        logger.info(f"Creating history entry for moodboard {moodboard_id} by user: {current_user['user_id']}")

        # Save history entry
        result = await supabase_service.save_moodboard_history(
            moodboard_id=moodboard_id,
            user_id=current_user["user_id"],
            change_type=request.change_type,
            tldraw_data_snapshot=request.tldraw_data_snapshot,
            change_description=request.change_description,
            is_auto_save=request.is_auto_save
        )

        if result:
            logger.info(f"Successfully created history entry for moodboard {moodboard_id}")
            return MoodboardOperationResponse(
                success=True,
                message="History entry created successfully",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create history entry"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating moodboard history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/stats/summary", response_model=MoodboardStatsResponse)
async def get_moodboard_stats(
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> MoodboardStatsResponse:
    """
    Get moodboard statistics for the current user.

    Returns summary statistics about the user's moodboards.
    """
    try:
        logger.info(f"Getting moodboard stats for user: {current_user['user_id']}")

        # Get all moodboards for stats calculation
        all_moodboards = await supabase_service.get_user_moodboards(
            user_id=current_user["user_id"],
            limit=1000,  # High limit to get all for stats
            status="",  # Get all statuses
            user_jwt_token=current_user.get("jwt_token")
        )

        # Calculate statistics
        total_moodboards = len(all_moodboards)
        active_moodboards = len([m for m in all_moodboards if m.get("status") == "active"])
        favorite_moodboards = len([m for m in all_moodboards if m.get("is_favorite", False)])
        public_moodboards = len([m for m in all_moodboards if m.get("is_public", False)])
        total_views = sum(m.get("view_count", 0) for m in all_moodboards)

        # Get recent activity (last 5 updated moodboards)
        recent_activity = [
            {
                "id": m["id"],
                "title": m["title"],
                "updated_at": m["updated_at"],
                "action": "updated"
            }
            for m in sorted(all_moodboards, key=lambda x: x["updated_at"], reverse=True)[:5]
        ]

        logger.info(f"Retrieved stats for user {current_user['user_id']}: {total_moodboards} total moodboards")

        return MoodboardStatsResponse(
            total_moodboards=total_moodboards,
            active_moodboards=active_moodboards,
            favorite_moodboards=favorite_moodboards,
            public_moodboards=public_moodboards,
            total_views=total_views,
            recent_activity=recent_activity
        )

    except Exception as e:
        logger.error(f"Error getting moodboard stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )
