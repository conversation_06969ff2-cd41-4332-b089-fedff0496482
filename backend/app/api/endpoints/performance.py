"""
Performance Monitoring API Endpoints for Emma Studio
Provides caching statistics, performance metrics, and optimization controls
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse

from app.services.caching_service import caching_service

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/cache/stats")
async def get_cache_statistics():
    """
    Get comprehensive cache statistics.
    
    This endpoint provides detailed information about cache usage,
    hit rates, and performance metrics for optimization monitoring.
    """
    try:
        # Initialize Redis connection if not already done
        if not caching_service.redis_client:
            await caching_service.initialize_redis()
        
        cache_stats = await caching_service.get_cache_stats()
        
        return JSONResponse(
            status_code=200,
            content={
                "cache_statistics": cache_stats,
                "service": "Performance Monitoring",
                "timestamp": str(datetime.utcnow()),
                "cache_enabled": caching_service.redis_client is not None
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get cache statistics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get cache statistics: {str(e)}"
        )

@router.post("/cache/clear")
async def clear_cache(
    prefix: Optional[str] = Query(None, description="Cache prefix to clear (optional)")
):
    """
    Clear cache entries.
    
    This endpoint allows clearing specific cache prefixes or all cache entries
    for cache management and troubleshooting purposes.
    """
    try:
        # Initialize Redis connection if not already done
        if not caching_service.redis_client:
            await caching_service.initialize_redis()
        
        result = await caching_service.clear_cache(prefix)
        
        return JSONResponse(
            status_code=200,
            content={
                "clear_result": result,
                "timestamp": str(datetime.utcnow()),
                "requested_prefix": prefix
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear cache: {str(e)}"
        )

@router.get("/cache/health")
async def check_cache_health():
    """
    Check cache service health.
    
    This endpoint provides health status of the Redis cache service
    and connection diagnostics.
    """
    try:
        # Try to initialize Redis connection
        redis_connected = await caching_service.initialize_redis()
        
        health_status = {
            "redis_connected": redis_connected,
            "cache_service": "healthy" if redis_connected else "unhealthy",
            "timestamp": str(datetime.utcnow())
        }
        
        if redis_connected:
            # Get basic Redis info
            try:
                info = await caching_service.redis_client.info()
                health_status.update({
                    "redis_version": info.get("redis_version", "unknown"),
                    "uptime_seconds": info.get("uptime_in_seconds", 0),
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory_human": info.get("used_memory_human", "unknown")
                })
            except Exception as e:
                logger.warning(f"Could not get Redis info: {e}")
        
        status_code = 200 if redis_connected else 503
        
        return JSONResponse(
            status_code=status_code,
            content=health_status
        )
        
    except Exception as e:
        logger.error(f"Cache health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "redis_connected": False,
                "cache_service": "error",
                "error": str(e),
                "timestamp": str(datetime.utcnow())
            }
        )

@router.get("/metrics")
async def get_performance_metrics():
    """
    Get comprehensive performance metrics.
    
    This endpoint provides performance statistics including response times,
    request counts, and system resource usage.
    """
    try:
        # Get cache statistics
        cache_stats = await caching_service.get_cache_stats()
        
        # Get performance middleware stats if available
        performance_stats = {}
        try:
            from app.middleware.caching_middleware import performance_middleware_instance
            if performance_middleware_instance:
                performance_stats = performance_middleware_instance.get_stats()
        except ImportError:
            logger.warning("Performance middleware not available")
        
        metrics = {
            "cache": cache_stats,
            "performance": performance_stats,
            "system": {
                "timestamp": str(datetime.utcnow()),
                "service": "Emma Studio API",
                "version": "1.0.0"
            }
        }
        
        return JSONResponse(
            status_code=200,
            content=metrics
        )
        
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get performance metrics: {str(e)}"
        )

@router.get("/optimization/recommendations")
async def get_optimization_recommendations():
    """
    Get performance optimization recommendations.
    
    This endpoint analyzes current performance metrics and provides
    actionable recommendations for improving system performance.
    """
    try:
        recommendations = []
        
        # Check cache health
        cache_stats = await caching_service.get_cache_stats()
        
        if cache_stats.get("status") == "disconnected":
            recommendations.append({
                "category": "caching",
                "priority": "high",
                "issue": "Redis cache is not connected",
                "recommendation": "Check Redis connection and configuration",
                "impact": "High - No response caching available"
            })
        elif cache_stats.get("status") == "connected":
            total_keys = cache_stats.get("total_keys", 0)
            
            if total_keys == 0:
                recommendations.append({
                    "category": "caching",
                    "priority": "medium",
                    "issue": "No cached data found",
                    "recommendation": "Cache is working but no data cached yet - this is normal for new deployments",
                    "impact": "Low - Cache will populate with usage"
                })
            elif total_keys > 10000:
                recommendations.append({
                    "category": "caching",
                    "priority": "medium",
                    "issue": "High number of cached keys",
                    "recommendation": "Consider implementing cache key expiration policies",
                    "impact": "Medium - May affect memory usage"
                })
        
        # Check for common optimization opportunities
        recommendations.extend([
            {
                "category": "api",
                "priority": "medium",
                "issue": "API response optimization",
                "recommendation": "Enable response compression for large payloads",
                "impact": "Medium - Reduces bandwidth usage"
            },
            {
                "category": "database",
                "priority": "low",
                "issue": "Database query optimization",
                "recommendation": "Monitor slow queries and add appropriate indexes",
                "impact": "Variable - Depends on query patterns"
            },
            {
                "category": "monitoring",
                "priority": "low",
                "issue": "Performance monitoring",
                "recommendation": "Implement detailed request/response time tracking",
                "impact": "Low - Improves observability"
            }
        ])
        
        return JSONResponse(
            status_code=200,
            content={
                "recommendations": recommendations,
                "total_recommendations": len(recommendations),
                "high_priority": len([r for r in recommendations if r["priority"] == "high"]),
                "medium_priority": len([r for r in recommendations if r["priority"] == "medium"]),
                "low_priority": len([r for r in recommendations if r["priority"] == "low"]),
                "generated_at": str(datetime.utcnow())
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get optimization recommendations: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get recommendations: {str(e)}"
        )

@router.post("/cache/warmup")
async def warmup_cache():
    """
    Warm up cache with frequently accessed data.
    
    This endpoint pre-loads commonly requested data into cache
    to improve initial response times.
    """
    try:
        # Initialize Redis connection if not already done
        if not caching_service.redis_client:
            redis_connected = await caching_service.initialize_redis()
            if not redis_connected:
                raise HTTPException(
                    status_code=503,
                    detail="Redis cache is not available"
                )
        
        warmup_results = {
            "started_at": str(datetime.utcnow()),
            "items_cached": 0,
            "errors": []
        }
        
        # Cache common structured data
        try:
            from app.services.structured_data_service import structured_data_service
            
            # Cache organization schema
            org_schema = structured_data_service.generate_organization_schema()
            if org_schema:
                await caching_service.cache_structured_data(
                    "organization", "default", org_schema, ttl=86400
                )
                warmup_results["items_cached"] += 1
            
            # Cache website schema
            website_schema = structured_data_service.generate_website_schema()
            if website_schema:
                await caching_service.cache_structured_data(
                    "website", "default", website_schema, ttl=86400
                )
                warmup_results["items_cached"] += 1
                
        except Exception as e:
            warmup_results["errors"].append(f"Structured data warmup failed: {str(e)}")
        
        # Cache common accessibility files
        try:
            from app.services.llm_accessibility_service import llm_accessibility_service
            
            # Cache robots.txt
            robots_content = llm_accessibility_service.generate_robots_txt()
            await caching_service.set("llm:robots_txt", robots_content, ttl=86400)
            warmup_results["items_cached"] += 1
            
            # Cache llms.txt
            llms_content = llm_accessibility_service.generate_llms_txt()
            await caching_service.set("llm:llms_txt", llms_content, ttl=86400)
            warmup_results["items_cached"] += 1
            
        except Exception as e:
            warmup_results["errors"].append(f"LLM accessibility warmup failed: {str(e)}")
        
        warmup_results["completed_at"] = str(datetime.utcnow())
        warmup_results["success"] = len(warmup_results["errors"]) == 0
        
        return JSONResponse(
            status_code=200,
            content=warmup_results
        )
        
    except Exception as e:
        logger.error(f"Cache warmup failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Cache warmup failed: {str(e)}"
        )

@router.get("/status")
async def get_performance_status():
    """
    Get overall performance service status.
    
    This endpoint provides a comprehensive overview of all performance
    and caching services status.
    """
    try:
        # Check cache health
        redis_connected = await caching_service.initialize_redis()
        
        status = {
            "service": "Performance Monitoring API",
            "version": "1.0.0",
            "status": "operational",
            "timestamp": str(datetime.utcnow()),
            "components": {
                "redis_cache": "healthy" if redis_connected else "unhealthy",
                "performance_monitoring": "healthy",
                "optimization_engine": "healthy"
            },
            "features": {
                "response_caching": redis_connected,
                "performance_metrics": True,
                "cache_management": redis_connected,
                "optimization_recommendations": True,
                "cache_warmup": redis_connected
            },
            "endpoints": {
                "cache_stats": "/api/v1/performance/cache/stats",
                "cache_clear": "/api/v1/performance/cache/clear",
                "cache_health": "/api/v1/performance/cache/health",
                "metrics": "/api/v1/performance/metrics",
                "recommendations": "/api/v1/performance/optimization/recommendations",
                "cache_warmup": "/api/v1/performance/cache/warmup"
            }
        }
        
        return JSONResponse(
            status_code=200,
            content=status
        )
        
    except Exception as e:
        logger.error(f"Failed to get performance status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get status: {str(e)}"
        )
