"""API endpoints for design complexity analysis using Gemini Vision."""

import logging
import base64
import io
import os
import time
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File
from PIL import Image
import google.generativeai as genai
import json

from app.core.config import settings
from app.core.auth import get_current_user_from_token
from app.core.supabase import SupabaseService, get_supabase_service

logger = logging.getLogger(__name__)
router = APIRouter()

def generate_image_specific_description(filename: str, analysis_results: Dict[str, Any]) -> str:
    """
    Generate a specific, meaningful description based on the analyzed image content and results.

    Args:
        filename: Original filename of the analyzed image
        analysis_results: Analysis results containing scores and areas

    Returns:
        A concise, image-specific description that adds UX value
    """
    try:
        # Extract key information from analysis results
        overall_score = analysis_results.get("score", 0)
        complexity_scores = analysis_results.get("complexity", {})

        # Determine dominant characteristics based on scores
        dominant_aspects = []

        # Analyze color complexity
        color_score = complexity_scores.get("color", 0)
        if color_score >= 8:
            dominant_aspects.append("diseño vibrante y colorido")
        elif color_score >= 6:
            dominant_aspects.append("paleta de colores equilibrada")
        elif color_score <= 3:
            dominant_aspects.append("diseño minimalista")

        # Analyze layout complexity
        composition_score = complexity_scores.get("composition", 0)
        if composition_score >= 8:
            dominant_aspects.append("composición compleja")
        elif composition_score >= 6:
            dominant_aspects.append("estructura bien organizada")
        elif composition_score <= 3:
            dominant_aspects.append("layout simple y limpio")

        # Analyze typography
        typography_score = complexity_scores.get("typography", 0)
        if typography_score >= 7:
            dominant_aspects.append("tipografía prominente")
        elif typography_score <= 3:
            dominant_aspects.append("enfoque visual no textual")

        # Determine file type context
        file_extension = filename.lower().split('.')[-1] if '.' in filename else ""
        file_type_context = ""

        if file_extension in ['jpg', 'jpeg', 'png', 'webp']:
            if 'logo' in filename.lower():
                file_type_context = "logotipo"
            elif any(word in filename.lower() for word in ['banner', 'header', 'hero']):
                file_type_context = "banner"
            elif any(word in filename.lower() for word in ['card', 'post', 'social']):
                file_type_context = "diseño para redes sociales"
            elif any(word in filename.lower() for word in ['web', 'site', 'page']):
                file_type_context = "diseño web"
            else:
                file_type_context = "diseño gráfico"

        # Generate description based on analysis
        if overall_score >= 85:
            quality_descriptor = "Excelente"
        elif overall_score >= 70:
            quality_descriptor = "Buen"
        elif overall_score >= 50:
            quality_descriptor = "Diseño"
        else:
            quality_descriptor = "Diseño"

        # Combine elements into a meaningful description
        description_parts = []

        if file_type_context:
            description_parts.append(f"{quality_descriptor} {file_type_context}")
        else:
            description_parts.append(f"{quality_descriptor} diseño")

        if dominant_aspects:
            if len(dominant_aspects) == 1:
                description_parts.append(f"con {dominant_aspects[0]}")
            elif len(dominant_aspects) == 2:
                description_parts.append(f"con {dominant_aspects[0]} y {dominant_aspects[1]}")
            else:
                description_parts.append(f"con {', '.join(dominant_aspects[:-1])} y {dominant_aspects[-1]}")

        # Add score context
        if overall_score >= 85:
            description_parts.append(f"(puntuación profesional: {overall_score}/100)")
        else:
            description_parts.append(f"(puntuación: {overall_score}/100)")

        final_description = " ".join(description_parts)

        # Ensure it's not too long (max 150 characters for good UX)
        if len(final_description) > 150:
            # Fallback to shorter version
            if file_type_context and dominant_aspects:
                final_description = f"{quality_descriptor} {file_type_context} con {dominant_aspects[0]} ({overall_score}/100)"
            else:
                final_description = f"{quality_descriptor} diseño analizado con puntuación de {overall_score}/100"

        return final_description

    except Exception as e:
        logger.warning(f"Error generating image-specific description: {str(e)}")
        # Fallback to a generic but still better description
        return f"Diseño analizado con puntuación de {analysis_results.get('score', 0)}/100"

async def verify_gemini_api_key():
    """Verify Gemini API key is configured."""
    if not settings.GEMINI_API_KEY:
        raise HTTPException(
            status_code=500,
            detail="Gemini API key required. Please configure GEMINI_API_KEY environment variable."
        )



def parse_visuai_analysis(gemini_response: str) -> Dict[str, Any]:
    """
    Parse Gemini's analysis response and extract complexity metrics for VisuAI.

    Args:
        gemini_response: Raw analysis text from Gemini Vision

    Returns:
        Structured complexity analysis with scores and VisuAI insights
    """
    try:
        # VisuAI Professional Design Analysis: Parse Gemini's expert response
        analysis_lower = gemini_response.lower()

        # 1. JERARQUÍA VISUAL (0-10) - Principio fundamental de diseño
        hierarchy_score = 5  # Default
        if any(phrase in analysis_lower for phrase in ["punto focal claro", "jerarquía clara", "focal point", "clear hierarchy"]):
            hierarchy_score = 9
        elif any(phrase in analysis_lower for phrase in ["jerarquía visual", "visual hierarchy", "organizado por importancia"]):
            hierarchy_score = 8
        elif any(phrase in analysis_lower for phrase in ["sin punto focal", "no focal point", "confuso", "unclear hierarchy"]):
            hierarchy_score = 3
        elif any(phrase in analysis_lower for phrase in ["elementos compiten", "competing elements", "falta jerarquía"]):
            hierarchy_score = 2

        # 2. BALANCE Y COMPOSICIÓN (0-10) - Regla de tercios, proporción áurea
        composition_score = 5  # Default
        if any(phrase in analysis_lower for phrase in ["regla de tercios", "rule of thirds", "golden ratio", "proporción áurea"]):
            composition_score = 9
        elif any(phrase in analysis_lower for phrase in ["bien balanceado", "well balanced", "simetría", "symmetry"]):
            composition_score = 8
        elif any(phrase in analysis_lower for phrase in ["desbalanceado", "unbalanced", "elementos dispersos", "scattered"]):
            composition_score = 3
        elif any(phrase in analysis_lower for phrase in ["composición pobre", "poor composition", "mal distribuido"]):
            composition_score = 2

        # 3. CONTRASTE Y LEGIBILIDAD (0-10) - Estándares WCAG
        contrast_score = 5  # Default
        if any(phrase in analysis_lower for phrase in ["contraste excelente", "excellent contrast", "muy legible", "highly readable"]):
            contrast_score = 9
        elif any(phrase in analysis_lower for phrase in ["buen contraste", "good contrast", "legible", "readable"]):
            contrast_score = 8
        elif any(phrase in analysis_lower for phrase in ["contraste bajo", "low contrast", "difícil de leer", "hard to read"]):
            contrast_score = 3
        elif any(phrase in analysis_lower for phrase in ["ilegible", "illegible", "contraste insuficiente", "poor contrast"]):
            contrast_score = 2

        # 4. ESPACIO EN BLANCO (0-10) - Principios de Dieter Rams
        whitespace_score = 5  # Default
        if any(phrase in analysis_lower for phrase in ["espacio negativo efectivo", "effective negative space", "respiración", "breathing room"]):
            whitespace_score = 9
        elif any(phrase in analysis_lower for phrase in ["buen uso del espacio", "good use of space", "limpio", "clean"]):
            whitespace_score = 8
        elif any(phrase in analysis_lower for phrase in ["abarrotado", "cluttered", "falta espacio", "cramped"]):
            whitespace_score = 3
        elif any(phrase in analysis_lower for phrase in ["muy abarrotado", "very cluttered", "sin respiración", "no breathing room"]):
            whitespace_score = 2

        # 5. COHERENCIA TIPOGRÁFICA (0-10) - Swiss Design principles
        typography_score = 5  # Default
        if any(phrase in analysis_lower for phrase in ["tipografía coherente", "consistent typography", "máximo 2-3 fuentes", "2-3 fonts"]):
            typography_score = 9
        elif any(phrase in analysis_lower for phrase in ["tipografía consistente", "consistent fonts", "jerarquía tipográfica"]):
            typography_score = 8
        elif any(phrase in analysis_lower for phrase in ["muchas fuentes", "many fonts", "inconsistente", "inconsistent typography"]):
            typography_score = 3
        elif any(phrase in analysis_lower for phrase in ["caótico tipográfico", "typographic chaos", "demasiadas fuentes"]):
            typography_score = 2

        # 6. PALETA DE COLOR (0-10) - Teoría del color profesional
        color_score = 5  # Default
        if any(phrase in analysis_lower for phrase in ["regla 60-30-10", "60-30-10 rule", "paleta armónica", "harmonious palette"]):
            color_score = 9
        elif any(phrase in analysis_lower for phrase in ["colores armónicos", "harmonious colors", "paleta coherente", "coherent palette"]):
            color_score = 8
        elif any(phrase in analysis_lower for phrase in ["demasiados colores", "too many colors", "paleta confusa", "confusing palette"]):
            color_score = 3
        elif any(phrase in analysis_lower for phrase in ["colores conflictivos", "conflicting colors", "sin armonía", "no harmony"]):
            color_score = 2

        # Calculate overall score (0-100) based on professional design principles
        # Weighted scoring: Hierarchy and Composition are most important
        overall_score = int((
            hierarchy_score * 0.25 +      # 25% - Most important
            composition_score * 0.25 +    # 25% - Very important
            contrast_score * 0.20 +       # 20% - Critical for usability
            whitespace_score * 0.15 +     # 15% - Important for clarity
            typography_score * 0.10 +     # 10% - Important but less weight
            color_score * 0.05            # 5% - Least critical
        ) * 10)
        overall_score = min(100, max(0, overall_score))

        # VisuAI Professional Recommendations based on design principles
        recommendations = []
        visuai_insights = []

        # 1. JERARQUÍA VISUAL - Principio más importante
        if hierarchy_score < 4:
            recommendations.append("🎯 CRÍTICO: Establece un punto focal claro. Usa tamaño, color o posición para crear jerarquía visual (Principio de Dieter Rams)")
        elif hierarchy_score < 7:
            recommendations.append("📍 MEJORA: Refuerza la jerarquía visual. Los elementos importantes deben destacar más (Principio de Apple Design)")
        else:
            recommendations.append("✅ EXCELENTE: Jerarquía visual clara y efectiva")

        # 2. COMPOSICIÓN Y BALANCE
        if composition_score < 4:
            recommendations.append("⚖️ CRÍTICO: Aplica la regla de tercios o proporción áurea. Rebalancea los elementos visuales")
        elif composition_score < 7:
            recommendations.append("📐 MEJORA: Mejora el balance visual. Considera la regla de tercios para posicionar elementos clave")
        else:
            recommendations.append("✅ EXCELENTE: Composición bien balanceada")

        # 3. CONTRASTE Y LEGIBILIDAD
        if contrast_score < 4:
            recommendations.append("🔍 CRÍTICO: Mejora el contraste para cumplir estándares WCAG. La legibilidad es fundamental")
        elif contrast_score < 7:
            recommendations.append("📖 MEJORA: Aumenta el contraste entre texto y fondo para mejor legibilidad")
        else:
            recommendations.append("✅ EXCELENTE: Contraste y legibilidad óptimos")

        # 4. ESPACIO EN BLANCO
        if whitespace_score < 4:
            recommendations.append("🌬️ CRÍTICO: Añade más espacio en blanco. 'Less is more' - Principio de Dieter Rams")
        elif whitespace_score < 7:
            recommendations.append("📏 MEJORA: Usa más espacio negativo para dar respiración al diseño")
        else:
            recommendations.append("✅ EXCELENTE: Uso efectivo del espacio en blanco")

        # 5. TIPOGRAFÍA
        if typography_score < 4:
            recommendations.append("🔤 CRÍTICO: Limita a máximo 2-3 familias tipográficas. Consistencia es clave (Swiss Design)")
        elif typography_score < 7:
            recommendations.append("📝 MEJORA: Mejora la consistencia tipográfica y jerarquía de texto")
        else:
            recommendations.append("✅ EXCELENTE: Tipografía coherente y profesional")

        # 6. COLOR
        if color_score < 4:
            recommendations.append("🎨 CRÍTICO: Simplifica la paleta. Aplica la regla 60-30-10 para armonía cromática")
        elif color_score < 7:
            recommendations.append("🌈 MEJORA: Refina la paleta de colores para mayor armonía")
        else:
            recommendations.append("✅ EXCELENTE: Paleta de color armónica")

        # Overall professional assessment
        if overall_score >= 85:
            recommendations.append("🏆 DISEÑO PROFESIONAL: Cumple con estándares de diseño de clase mundial")
        elif overall_score >= 70:
            recommendations.append("👍 BUEN DISEÑO: Sólido, con oportunidades de refinamiento")
        elif overall_score >= 50:
            recommendations.append("⚠️ NECESITA MEJORAS: Enfócate en jerarquía y composición primero")
        else:
            recommendations.append("🚨 REDISEÑO RECOMENDADO: Aplica principios fundamentales de diseño")

        # Add professional insights
        visuai_insights.append("Análisis basado en principios de Dieter Rams, Paul Rand y estándares de Apple/Google")
        visuai_insights.append(f"Puntuación profesional: {overall_score}/100")
        visuai_insights.append(f"Jerarquía: {hierarchy_score}/10 | Composición: {composition_score}/10 | Contraste: {contrast_score}/10")

        # Create professional areas analysis
        areas = [
            {
                "name": "Jerarquía Visual",
                "score": hierarchy_score,
                "description": f"Principio fundamental de diseño - Puntuación: {hierarchy_score}/10 (Peso: 25%)"
            },
            {
                "name": "Composición y Balance",
                "score": composition_score,
                "description": f"Regla de tercios y proporción áurea - Puntuación: {composition_score}/10 (Peso: 25%)"
            },
            {
                "name": "Contraste y Legibilidad",
                "score": contrast_score,
                "description": f"Estándares WCAG y usabilidad - Puntuación: {contrast_score}/10 (Peso: 20%)"
            },
            {
                "name": "Espacio en Blanco",
                "score": whitespace_score,
                "description": f"Principios de Dieter Rams - Puntuación: {whitespace_score}/10 (Peso: 15%)"
            },
            {
                "name": "Coherencia Tipográfica",
                "score": typography_score,
                "description": f"Swiss Design principles - Puntuación: {typography_score}/10 (Peso: 10%)"
            },
            {
                "name": "Armonía Cromática",
                "score": color_score,
                "description": f"Teoría del color profesional - Puntuación: {color_score}/10 (Peso: 5%)"
            }
        ]

        return {
            "score": overall_score,
            "complexity": {
                "hierarchy": hierarchy_score,
                "composition": composition_score,
                "contrast": contrast_score,
                "whitespace": whitespace_score,
                "typography": typography_score,
                "color": color_score
            },
            "areas": areas,
            "recommendations": recommendations,
            "visuai_insights": visuai_insights,
            "analysis_summary": generate_image_specific_description(
                filename="unknown.jpg",  # Will be updated with actual filename in the main function
                analysis_results={
                    "score": overall_score,
                    "complexity": {
                        "hierarchy": hierarchy_score,
                        "composition": composition_score,
                        "contrast": contrast_score,
                        "whitespace": whitespace_score,
                        "typography": typography_score,
                        "color": color_score
                    }
                }
            ),
            "agent_name": "VisuAI",
            "agent_message": f"¡Hola! Soy VisuAI, tu experto en análisis de diseño profesional. He evaluado tu diseño con criterios de clase mundial: {overall_score}/100. {'🏆 ¡Diseño profesional!' if overall_score >= 85 else '👍 ¡Buen trabajo!' if overall_score >= 70 else '⚠️ ¡Oportunidades de mejora!' if overall_score >= 50 else '🚨 ¡Necesita rediseño!'}",
            "gemini_analysis": gemini_response[:300] + "..." if len(gemini_response) > 300 else gemini_response,
            "design_principles": {
                "methodology": "Análisis basado en principios de diseño profesional",
                "references": ["Dieter Rams - 10 Principles", "Paul Rand - Design Philosophy", "Apple Human Interface Guidelines", "Google Material Design", "WCAG Accessibility Standards"],
                "weighting": "Jerarquía (25%) + Composición (25%) + Contraste (20%) + Espacio (15%) + Tipografía (10%) + Color (5%)"
            }
        }

    except Exception as e:
        logger.error(f"Error analyzing complexity: {str(e)}")
        # Return default analysis on error
        return {
            "score": 50,
            "complexity": {"color": 5, "layout": 5, "typography": 5},
            "areas": [
                {"name": "Uso del color", "score": 5, "description": "Análisis no disponible"},
                {"name": "Estructura y layout", "score": 5, "description": "Análisis no disponible"},
                {"name": "Tipografía", "score": 5, "description": "Análisis no disponible"}
            ],
            "recommendations": ["No se pudo completar el análisis detallado"],
            "analysis_summary": "Análisis básico completado"
        }

async def analyze_design_with_gemini_vision(image_content: bytes) -> str:
    """
    Analyze design using Gemini Vision for VisuAI.

    Args:
        image_content: Raw image bytes

    Returns:
        Detailed analysis text from Gemini Vision
    """
    try:
        # Configure Gemini
        genai.configure(api_key=settings.GEMINI_API_KEY)

        # Initialize Gemini 1.5 Flash model (supports vision)
        model = genai.GenerativeModel('gemini-1.5-flash')

        # Convert image bytes to PIL Image
        image = Image.open(io.BytesIO(image_content))

        # Create the VisuAI analysis prompt with professional design criteria
        prompt = """Eres VisuAI, un experto en diseño visual con criterios de diseñadores profesionales como Dieter Rams, Paul Rand y los principios de diseño de Apple/Google. Analiza esta imagen usando criterios REALES de diseño:

**ANÁLISIS PROFESIONAL REQUERIDO:**

1. **JERARQUÍA VISUAL** (Principio fundamental):
   - ¿Hay un punto focal claro? ¿Dónde va primero la mirada?
   - ¿Los elementos están organizados por importancia?
   - ¿Se usa tamaño, color, posición para crear jerarquía?

2. **BALANCE Y COMPOSICIÓN** (Regla de tercios, Golden Ratio):
   - ¿Los elementos están balanceados visualmente?
   - ¿Se respeta la regla de tercios o proporciones áureas?
   - ¿Hay simetría o asimetría intencional?

3. **CONTRASTE Y LEGIBILIDAD** (Estándares WCAG):
   - ¿El contraste entre texto y fondo es suficiente?
   - ¿Los colores crean suficiente diferenciación?
   - ¿Es fácil distinguir elementos importantes?

4. **ESPACIO EN BLANCO** (Minimalismo, Dieter Rams):
   - ¿Se usa el espacio negativo efectivamente?
   - ¿Hay suficiente respiración entre elementos?
   - ¿El diseño se siente abarrotado o limpio?

5. **COHERENCIA TIPOGRÁFICA** (Swiss Design):
   - ¿Cuántas familias tipográficas se usan? (Máximo 2-3 es ideal)
   - ¿Hay consistencia en tamaños y pesos?
   - ¿La tipografía refuerza la jerarquía?

6. **PALETA DE COLOR** (Teoría del color):
   - ¿Cuántos colores se usan? (Regla 60-30-10)
   - ¿Los colores son armónicos o complementarios?
   - ¿Hay un color dominante claro?

**RESPONDE CON CRITERIOS ESPECÍFICOS:**
- Menciona EXACTAMENTE qué principios de diseño se cumplen o fallan
- Da puntuaciones específicas basadas en estándares profesionales
- Usa terminología de diseño real (no palabras genéricas)
- Compara con mejores prácticas de la industria"""

        # Make the API call to Gemini Vision
        response = model.generate_content([prompt, image])

        if not response.text:
            raise Exception("Gemini returned empty response")

        logger.info(f"Gemini Vision analysis completed: {len(response.text)} characters")
        return response.text

    except Exception as e:
        logger.error(f"Error calling Gemini Vision API: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"VisuAI failed to analyze image with Gemini: {str(e)}"
        )

@router.post("/analyze-design")
async def analyze_design_complexity(
    design: UploadFile = File(..., description="Design image file (JPEG, PNG, WebP)"),
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Analyze the visual complexity of a design using VisuAI powered by Gemini Vision.

    VisuAI is your intelligent design analysis assistant that uses Gemini Vision AI
    to analyze design elements and calculate complexity scores for color, layout, and typography.
    """
    logger.info(f"VisuAI received design analysis request for file: {design.filename}")

    try:
        # Verify Gemini API key
        await verify_gemini_api_key()

        # Validate file type
        if not design.content_type or not design.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="File must be an image (JPEG, PNG, WebP)"
            )

        # Validate file size (max 10MB)
        max_size = 10 * 1024 * 1024  # 10MB
        image_content = await design.read()

        if len(image_content) > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: 10MB, received: {len(image_content) / 1024 / 1024:.2f}MB"
            )

        # Validate image format using PIL
        try:
            image = Image.open(io.BytesIO(image_content))
            image.verify()  # Verify it's a valid image
            logger.info(f"Image validated: {image.format}, {image.size}")
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid image file: {str(e)}"
            )

        # Record start time for analysis duration
        start_time = time.time()

        # Analyze with VisuAI using Gemini Vision
        logger.info("VisuAI starting Gemini Vision analysis...")
        gemini_analysis = await analyze_design_with_gemini_vision(image_content)
        complexity_results = parse_visuai_analysis(gemini_analysis)

        # ✅ FIXED: Update analysis summary with actual filename and results
        complexity_results["analysis_summary"] = generate_image_specific_description(
            filename=design.filename or "unknown.jpg",
            analysis_results=complexity_results
        )

        # Calculate analysis duration
        analysis_duration_ms = int((time.time() - start_time) * 1000)

        logger.info(f"VisuAI analysis completed. Overall score: {complexity_results['score']}")

        # Upload image to storage and prepare analysis data for database storage
        file_url = None
        if current_user["user_id"] != "anonymous":
            try:
                # Upload image to Supabase Storage
                logger.info(f"📤 Uploading image to Supabase Storage for user: {current_user['user_id']}")
                logger.info(f"📊 Image details: filename={design.filename}, size={len(image_content)} bytes, type={design.content_type}")

                file_url = await supabase_service.upload_image_to_storage(
                    user_id=current_user["user_id"],
                    image_content=image_content,
                    original_filename=design.filename or "unknown.jpg",
                    content_type=design.content_type or "image/jpeg",
                    user_jwt_token=current_user.get("jwt_token")  # ✅ Pass JWT token for authentication
                )

                if file_url:
                    logger.info(f"✅ Image uploaded successfully to: {file_url}")
                else:
                    logger.warning("⚠️ Failed to upload image to storage - continuing without image")

            except Exception as e:
                logger.error(f"❌ Exception during image upload: {str(e)}")
                logger.error(f"🔍 Exception type: {type(e).__name__}")
                # Continue without image storage if upload fails

        # Prepare analysis data for database storage (now including file_url)
        analysis_data = {
            "original_filename": design.filename or "unknown.jpg",
            "file_size": len(image_content),
            "file_type": design.content_type,
            "file_url": file_url,  # ✅ NOW INCLUDES file_url!
            "analysis_duration_ms": analysis_duration_ms,
            **complexity_results
        }

        # Save analysis to Supabase (only if user is authenticated)
        saved_analysis = None
        if current_user["user_id"] != "anonymous":
            try:
                logger.info(f"💾 Saving analysis to database for user: {current_user['user_id']}")
                logger.info(f"📊 Analysis data includes file_url: {bool(file_url)}")
                if file_url:
                    logger.info(f"🔗 File URL: {file_url}")

                saved_analysis = await supabase_service.save_design_analysis(
                    user_id=current_user["user_id"],
                    analysis_data=analysis_data
                )
                if saved_analysis:
                    logger.info(f"✅ Analysis saved to database with ID: {saved_analysis['id']}")
                    logger.info(f"📊 Saved analysis file_url: {saved_analysis.get('file_url', 'NOT SET')}")
                else:
                    logger.warning("⚠️ Failed to save analysis to database - no data returned")
                    # If database save failed but image was uploaded, clean up the image
                    if file_url:
                        logger.info("🧹 Cleaning up uploaded image due to database save failure")
                        await supabase_service.delete_image_from_storage(file_url)
            except Exception as e:
                logger.error(f"❌ Exception saving analysis to database: {str(e)}")
                logger.error(f"🔍 Exception type: {type(e).__name__}")
                # If database save failed but image was uploaded, clean up the image
                if file_url:
                    logger.info("🧹 Cleaning up uploaded image due to database save exception")
                    await supabase_service.delete_image_from_storage(file_url)
                # Don't fail the request if database save fails

        # Prepare response
        response_data = {
            "success": True,
            "message": "VisuAI analysis completed successfully with Gemini Vision",
            "agent": "VisuAI",
            "powered_by": "Gemini Vision AI",
            "analysis_duration_ms": analysis_duration_ms,
            **complexity_results
        }

        # Add database ID if saved successfully
        if saved_analysis:
            response_data["analysis_id"] = saved_analysis["id"]
            response_data["saved_to_database"] = True
        else:
            response_data["saved_to_database"] = False

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in design analysis: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during design analysis: {str(e)}"
        )


@router.get("/analyses")
async def get_user_analyses(
    limit: int = 50,
    tool_type: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Get all design analyses for the current user.
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to access saved analyses"
        )

    try:
        analyses = await supabase_service.get_user_analyses(
            user_id=current_user["user_id"],
            limit=limit,
            tool_type=tool_type
        )

        return {
            "success": True,
            "analyses": analyses,
            "count": len(analyses),
            "user_id": current_user["user_id"]
        }

    except Exception as e:
        logger.error(f"Error fetching user analyses: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch analyses: {str(e)}"
        )


@router.get("/analyses/{analysis_id}")
async def get_analysis_by_id(
    analysis_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Get a specific design analysis by ID.
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to access saved analyses"
        )

    try:
        analysis = await supabase_service.get_analysis_by_id(
            analysis_id=analysis_id,
            user_id=current_user["user_id"]
        )

        if not analysis:
            raise HTTPException(
                status_code=404,
                detail="Analysis not found or access denied"
            )

        return {
            "success": True,
            "analysis": analysis
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching analysis: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch analysis: {str(e)}"
        )


@router.patch("/analyses/{analysis_id}/favorite")
async def toggle_analysis_favorite(
    analysis_id: str,
    is_favorite: bool,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Toggle the favorite status of an analysis.
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to modify analyses"
        )

    try:
        success = await supabase_service.update_analysis_favorite(
            analysis_id=analysis_id,
            user_id=current_user["user_id"],
            is_favorite=is_favorite
        )

        if not success:
            raise HTTPException(
                status_code=404,
                detail="Analysis not found or access denied"
            )

        return {
            "success": True,
            "message": f"Analysis {'added to' if is_favorite else 'removed from'} favorites",
            "is_favorite": is_favorite
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating favorite status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update favorite status: {str(e)}"
        )


@router.delete("/analyses/{analysis_id}")
async def delete_analysis(
    analysis_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Delete a design analysis.
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to delete analyses"
        )

    try:
        success = await supabase_service.delete_analysis(
            analysis_id=analysis_id,
            user_id=current_user["user_id"]
        )

        if not success:
            raise HTTPException(
                status_code=404,
                detail="Analysis not found or access denied"
            )

        return {
            "success": True,
            "message": "Analysis deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting analysis: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete analysis: {str(e)}"
        )
