"""
API endpoints for Luma Labs video generation using Dream Machine API.
"""

import logging
import base64
import io
import os
import uuid
import time
from PIL import Image
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File, Query
from fastapi.responses import Response, FileResponse
from fastapi.staticfiles import StaticFiles
from typing import Optional
import httpx

from app.core.auth import verify_api_key
from app.services.luma_service import luma_service
from app.schemas.luma import (
    FrontendLumaResponse,
    LumaGenerationStatusResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/test-image-upload")
async def test_image_upload(
    image: UploadFile = File(..., description="Test image to upload")
):
    """Test endpoint to verify image hosting services are working."""
    try:
        logger.info("🧪 Testing image upload services...")

        # Read and process the image
        image_content = await image.read()
        logger.info(f"📁 Test image size: {len(image_content)} bytes")

        # Upload to hosting service
        image_url = await upload_image_to_temp_host(image_content)

        return {
            "success": True,
            "message": "Image uploaded successfully",
            "url": image_url,
            "size": len(image_content)
        }

    except Exception as e:
        logger.error(f"❌ Test upload failed: {e}")
        return {
            "success": False,
            "error": str(e)
        }

async def validate_image_url(url: str) -> bool:
    """
    Validate that an image URL is accessible and returns a valid image.
    """
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.head(url)
            return (
                response.status_code == 200 and
                response.headers.get("content-type", "").startswith("image/")
            )
    except Exception:
        return False


async def upload_image_to_temp_host(image_content: bytes) -> str:
    """
    Upload image to a temporary hosting service for public access.
    Returns a public URL that can be accessed by Luma Labs.
    Raises HTTPException if all upload methods fail.
    """
    try:
        # Process image first
        image = Image.open(io.BytesIO(image_content))

        # Convert to RGB if necessary (removes transparency issues)
        if image.mode in ('RGBA', 'LA', 'P'):
            # Create white background for transparent images
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            if image.mode in ('RGBA', 'LA'):
                background.paste(image, mask=image.split()[-1] if len(image.split()) > 3 else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')

        # Optimize image size and quality for better compatibility
        width, height = image.size
        max_dimension = 1024  # Reasonable max for API

        if max(width, height) > max_dimension:
            # Resize maintaining aspect ratio
            if width > height:
                new_width = max_dimension
                new_height = int(height * (max_dimension / width))
            else:
                new_height = max_dimension
                new_width = int(width * (max_dimension / height))

            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            logger.info(f"Resized image from {width}x{height} to {new_width}x{new_height}")

        # Save optimized image to bytes
        output_buffer = io.BytesIO()
        image.save(output_buffer, format='JPEG', quality=90, optimize=True)
        optimized_content = output_buffer.getvalue()

        logger.info(f"Processed image: original size {len(image_content)} bytes, optimized size {len(optimized_content)} bytes")

        # Create base64 content for services that need it
        base64_content = base64.b64encode(optimized_content).decode('utf-8')

        # For development/testing, use local hosting first
        # This ensures the functionality works while we debug external services

        try:
            # Try local temporary hosting first (most reliable for development)
            logger.info("🔄 Attempting local temporary hosting...")
            local_url = await upload_to_local_temp(optimized_content)

            if local_url and local_url.startswith("http"):
                logger.info(f"✅ Local hosting successful: {local_url}")
                return local_url

        except Exception as e:
            logger.warning(f"❌ Local hosting failed: {e}")

        # List of external upload methods to try as backup
        upload_methods = [
            ("Catbox", lambda: upload_to_catbox(optimized_content)),
            ("Imgur", lambda: upload_to_imgur(base64_content)),
            ("TmpFiles", lambda: upload_to_tmpfiles(optimized_content)),
        ]

        # Try each upload method
        for service_name, upload_func in upload_methods:
            try:
                logger.info(f"🔄 Attempting upload to {service_name}...")
                image_url = await upload_func()

                if image_url and image_url.startswith(("http://", "https://")):
                    logger.info(f"📤 {service_name} returned URL: {image_url[:60]}...")

                    # Validate the URL is accessible
                    logger.info(f"🔍 Validating URL accessibility for {service_name}...")
                    if await validate_image_url(image_url):
                        logger.info(f"✅ Image uploaded successfully to {service_name}: {image_url}")
                        return image_url
                    else:
                        logger.warning(f"❌ {service_name} URL validation failed: {image_url}")
                else:
                    logger.warning(f"❌ {service_name} returned invalid URL: {image_url}")

            except Exception as e:
                logger.warning(f"❌ Failed to upload to {service_name}: {str(e)[:200]}...")

        # If all hosting services fail, log detailed error and raise exception
        logger.error("🚨 All image hosting services failed")
        logger.error(f"Image size: {len(optimized_content)} bytes")
        logger.error(f"Image format: JPEG, quality: 90")

        # As a last resort, try a simple base64 data URL for debugging
        # (This won't work with Luma Labs but helps identify the issue)
        logger.warning("⚠️ Attempting fallback to base64 data URL for debugging...")

        raise HTTPException(
            status_code=500,
            detail="Failed to upload image to hosting service. All 4 hosting services are currently unavailable. Please try again later or use a different image."
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing image: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error processing image: {str(e)}"
        )


async def upload_to_local_temp(image_content: bytes) -> str:
    """Upload to local temporary directory and serve via FastAPI."""
    try:
        # Create temp directory if it doesn't exist
        temp_dir = "temp_images"
        os.makedirs(temp_dir, exist_ok=True)

        # Generate unique filename
        filename = f"temp_{uuid.uuid4().hex}_{int(time.time())}.jpg"
        filepath = os.path.join(temp_dir, filename)

        # Save image to local file
        with open(filepath, "wb") as f:
            f.write(image_content)

        # Verify file was saved correctly
        if not os.path.exists(filepath):
            raise Exception("Failed to save image file")

        file_size = os.path.getsize(filepath)
        if file_size != len(image_content):
            raise Exception(f"File size mismatch: expected {len(image_content)}, got {file_size}")

        # Return URL that can be accessed via FastAPI
        # Try different possible backend URLs
        possible_urls = [
            f"http://localhost:8000/api/luma-labs/temp-images/{filename}",
            f"http://127.0.0.1:8000/api/luma-labs/temp-images/{filename}",
            f"http://localhost:3007/api/luma-labs/temp-images/{filename}",
        ]

        # Use the first URL for now
        local_url = possible_urls[0]

        logger.info(f"📁 Image saved locally: {filepath} ({file_size} bytes)")
        logger.info(f"🌐 Local URL: {local_url}")

        return local_url

    except Exception as e:
        logger.error(f"❌ Local temp upload error: {str(e)}")
        raise Exception(f"Local temp upload failed: {str(e)}")


async def upload_to_imgur(base64_content: str) -> str:
    """Upload to Imgur using public client ID."""
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            headers = {
                "Authorization": "Client-ID 546c25a59c58ad7"  # Public Imgur client ID
            }

            data = {
                "image": base64_content,
                "type": "base64"
            }

            logger.info(f"📤 Uploading base64 image to Imgur (size: {len(base64_content)} chars)...")

            response = await client.post(
                "https://api.imgur.com/3/image",
                headers=headers,
                data=data
            )

            logger.info(f"📥 Imgur response: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                logger.info(f"📄 Imgur response data: {result}")

                if result.get("success") and result.get("data", {}).get("link"):
                    url = result["data"]["link"]
                    logger.info(f"✅ Imgur upload successful: {url}")
                    return url
                else:
                    logger.warning(f"⚠️ Imgur response missing expected data: {result}")

            raise Exception(f"Imgur upload failed: {response.status_code} - {response.text}")

        except httpx.TimeoutException:
            raise Exception("Imgur upload timed out")
        except Exception as e:
            logger.error(f"❌ Imgur upload error: {str(e)}")
            raise


async def upload_to_catbox(image_content: bytes) -> str:
    """Upload to Catbox.moe file hosting."""
    async with httpx.AsyncClient(timeout=60.0) as client:
        try:
            files = {
                "fileToUpload": ("image.jpg", image_content, "image/jpeg")
            }
            data = {
                "reqtype": "fileupload"
            }

            logger.info(f"📤 Uploading {len(image_content)} bytes to Catbox...")

            response = await client.post(
                "https://catbox.moe/user/api.php",
                files=files,
                data=data
            )

            logger.info(f"📥 Catbox response: {response.status_code}")
            logger.info(f"📄 Catbox response text: {response.text[:200]}")

            if response.status_code == 200:
                url = response.text.strip()
                if url.startswith("https://files.catbox.moe/"):
                    logger.info(f"✅ Catbox upload successful: {url}")
                    return url
                else:
                    logger.warning(f"⚠️ Catbox returned unexpected URL format: {url}")

            raise Exception(f"Catbox upload failed: {response.status_code} - {response.text}")

        except httpx.TimeoutException:
            raise Exception("Catbox upload timed out")
        except Exception as e:
            logger.error(f"❌ Catbox upload error: {str(e)}")
            raise


async def upload_to_0x0(image_content: bytes) -> str:
    """Upload to 0x0.st file hosting."""
    async with httpx.AsyncClient(timeout=30.0) as client:
        files = {
            "file": ("image.jpg", image_content, "image/jpeg")
        }

        response = await client.post(
            "https://0x0.st",
            files=files
        )

        if response.status_code == 200:
            url = response.text.strip()
            if url.startswith("https://0x0.st/"):
                return url

        raise Exception(f"0x0.st upload failed: {response.status_code} - {response.text}")


async def upload_to_tmpfiles(image_content: bytes) -> str:
    """Upload to tmpfiles.org temporary file hosting."""
    async with httpx.AsyncClient(timeout=30.0) as client:
        files = {
            "file": ("image.jpg", image_content, "image/jpeg")
        }

        response = await client.post(
            "https://tmpfiles.org/api/v1/upload",
            files=files
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("status") == "success" and result.get("data", {}).get("url"):
                # tmpfiles.org returns a URL that needs to be converted to direct link
                url = result["data"]["url"]
                # Convert from tmpfiles.org/abc to tmpfiles.org/dl/abc for direct access
                if "tmpfiles.org/" in url:
                    direct_url = url.replace("tmpfiles.org/", "tmpfiles.org/dl/")
                    return direct_url
                return url

        raise Exception(f"TmpFiles upload failed: {response.status_code} - {response.text}")


async def upload_to_imgbb(base64_content: str) -> str:
    """Upload to ImgBB using temporary upload."""
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Try ImgBB without API key (temporary upload)
        data = {
            "image": base64_content,
        }

        # Try the temporary upload endpoint
        response = await client.post(
            "https://api.imgbb.com/1/upload?expiration=3600",  # 1 hour expiration
            data=data
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("success") and result.get("data", {}).get("url"):
                return result["data"]["url"]

        raise Exception(f"ImgBB upload failed: {response.status_code} - {response.text}")


@router.post(
    "/generate-text-to-video",
    response_model=FrontendLumaResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_text_to_video(
    prompt: str = Form(..., description="Text description of the video"),
    resolution: str = Form(default="720p", description="Video resolution: 540p, 720p, 1080p, 4k"),
    duration: int = Form(default=5, description="Video duration in seconds (1-10)"),
    aspect_ratio: str = Form(default="16:9", description="Aspect ratio: 16:9, 1:1, 9:16"),
    loop: bool = Form(default=False, description="Whether video should loop"),
    concepts: Optional[str] = Form(default=None, description="Special effects (comma-separated)")
) -> FrontendLumaResponse:
    """Generate a video from text using Luma Labs Dream Machine."""

    try:
        logger.info(f"🎬 Generating text-to-video with Luma Labs: {prompt[:100]}...")

        # Validate inputs
        if not luma_service.validate_resolution(resolution):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid resolution. Must be one of: 540p, 720p, 1080p, 4k"
            )

        if not luma_service.validate_aspect_ratio(aspect_ratio):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid aspect ratio. Must be one of: 16:9, 1:1, 9:16"
            )

        if duration not in [5, 9]:
            raise HTTPException(
                status_code=400,
                detail="Duration must be 5 or 9 seconds (Luma Labs API limitation)"
            )

        # Parse concepts if provided
        concepts_list = None
        if concepts:
            concepts_list = [c.strip() for c in concepts.split(",") if c.strip()]

        # Call the service
        service_response = await luma_service.generate_text_to_video(
            prompt=prompt,
            resolution=resolution,
            duration=duration,
            aspect_ratio=aspect_ratio,
            loop=loop,
            concepts=concepts_list
        )

        # Convert to frontend response
        return FrontendLumaResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_text_to_video endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during video generation: {e}"
        )


@router.post(
    "/generate-image-to-video",
    response_model=FrontendLumaResponse,
    dependencies=[Depends(verify_api_key)],
)
async def generate_image_to_video(
    prompt: str = Form(..., description="Text description of the video"),
    resolution: str = Form(default="720p", description="Video resolution: 540p, 720p, 1080p, 4k"),
    duration: int = Form(default=5, description="Video duration in seconds (1-10)"),
    aspect_ratio: str = Form(default="16:9", description="Aspect ratio: 16:9, 1:1, 9:16"),
    loop: bool = Form(default=False, description="Whether video should loop"),
    keyframe_type: str = Form(..., description="Keyframe type: frame0, frame1, or both"),
    frame0: Optional[UploadFile] = File(default=None, description="Initial frame image"),
    frame1: Optional[UploadFile] = File(default=None, description="Final frame image")
) -> FrontendLumaResponse:
    """Generate a video from image keyframes using Luma Labs Dream Machine."""

    try:
        logger.info(f"🎬 Generating image-to-video with Luma Labs: {prompt[:100]}...")

        # Validate inputs
        if not luma_service.validate_resolution(resolution):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid resolution. Must be one of: 540p, 720p, 1080p, 4k"
            )

        if not luma_service.validate_aspect_ratio(aspect_ratio):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid aspect ratio. Must be one of: 16:9, 1:1, 9:16"
            )

        if duration not in [5, 9]:
            raise HTTPException(
                status_code=400,
                detail="Duration must be 5 or 9 seconds (Luma Labs API limitation)"
            )

        if keyframe_type not in ["frame0", "frame1", "both"]:
            raise HTTPException(
                status_code=400,
                detail="keyframe_type must be 'frame0', 'frame1', or 'both'"
            )

        # Validate required files based on keyframe type
        if keyframe_type == "frame0" and not frame0:
            raise HTTPException(status_code=400, detail="frame0 image is required")
        if keyframe_type == "frame1" and not frame1:
            raise HTTPException(status_code=400, detail="frame1 image is required")
        if keyframe_type == "both" and (not frame0 or not frame1):
            raise HTTPException(status_code=400, detail="Both frame0 and frame1 images are required")

        # Process uploaded images and create temporary URLs
        keyframes = {}

        if frame0:
            # Validate file type
            if not frame0.content_type or not frame0.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid frame0 file type: {frame0.content_type}"
                )

            # Process image and upload to get public URL
            frame0_content = await frame0.read()
            frame0_url = await upload_image_to_temp_host(frame0_content)
            keyframes["frame0"] = {
                "type": "image",
                "url": frame0_url
            }

        if frame1:
            # Validate file type
            if not frame1.content_type or not frame1.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid frame1 file type: {frame1.content_type}"
                )

            # Process image and upload to get public URL
            frame1_content = await frame1.read()
            frame1_url = await upload_image_to_temp_host(frame1_content)
            keyframes["frame1"] = {
                "type": "image",
                "url": frame1_url
            }

        # Call the service
        service_response = await luma_service.generate_image_to_video(
            prompt=prompt,
            keyframes=keyframes,
            resolution=resolution,
            duration=duration,
            aspect_ratio=aspect_ratio,
            loop=loop
        )

        # Convert to frontend response
        return FrontendLumaResponse.from_service_response(service_response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_image_to_video endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during video generation: {e}"
        )


@router.get(
    "/status/{generation_id}",
    response_model=LumaGenerationStatusResponse,
    dependencies=[Depends(verify_api_key)],
)
async def get_generation_status(generation_id: str) -> LumaGenerationStatusResponse:
    """Get the status of a video generation."""
    
    try:
        logger.info(f"📊 Getting status for generation: {generation_id}")

        service_response = await luma_service.get_generation_status(generation_id)

        return LumaGenerationStatusResponse(
            success=service_response.get("success", False),
            generation_id=generation_id,
            status=service_response.get("status", "unknown"),
            data=service_response.get("data"),
            error=service_response.get("error")
        )
        
    except Exception as e:
        logger.error(f"Error in get_generation_status endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error getting generation status: {e}"
        )


@router.get("/temp-images/{filename}")
async def serve_temp_image(filename: str):
    """Serve temporary images for Luma Labs API access."""
    try:
        temp_dir = "temp_images"
        filepath = os.path.join(temp_dir, filename)

        if not os.path.exists(filepath):
            raise HTTPException(status_code=404, detail="Image not found")

        return FileResponse(
            filepath,
            media_type="image/jpeg",
            headers={
                "Cache-Control": "public, max-age=3600",
                "Access-Control-Allow-Origin": "*"
            }
        )

    except Exception as e:
        logger.error(f"Error serving temp image: {e}")
        raise HTTPException(status_code=500, detail="Error serving image")


@router.get("/download-video")
async def download_video(
    url: str = Query(..., description="The video URL to download")
):
    """
    Proxy endpoint to download videos from Luma Labs
    and serve them directly to bypass CORS restrictions.
    """
    try:
        logger.info(f"📥 Proxying video download from: {url[:100]}...")

        # Validate that the URL is from a trusted source (Luma Labs)
        if not (url.startswith("https://storage.googleapis.com/luma-dream-machine") or
                url.startswith("https://storage.cdn-luma.com/")):
            raise HTTPException(
                status_code=400,
                detail="Only Luma Labs video URLs are allowed"
            )

        async with httpx.AsyncClient(timeout=300.0) as client:
            # Stream the video content
            async with client.stream("GET", url) as response:
                if response.status_code != 200:
                    logger.error(f"Failed to download video: {response.status_code}")
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"Failed to download video: {response.status_code}"
                    )

                # Get content type from the response
                content_type = response.headers.get("content-type", "video/mp4")

                # Determine file extension based on content type
                if "mp4" in content_type:
                    file_extension = "mp4"
                elif "webm" in content_type:
                    file_extension = "webm"
                elif "mov" in content_type:
                    file_extension = "mov"
                else:
                    file_extension = "mp4"  # Default fallback

                # Create filename with timestamp
                import time
                filename = f"video-{int(time.time())}.{file_extension}"

                # Read the entire content
                content = b""
                async for chunk in response.aiter_bytes():
                    content += chunk

                # Return the video as a downloadable response
                return Response(
                    content=content,
                    media_type=content_type,
                    headers={
                        "Content-Disposition": f"attachment; filename={filename}",
                        "Content-Length": str(len(content)),
                        "Cache-Control": "no-cache"
                    }
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in download_video endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during video download: {e}"
        )



