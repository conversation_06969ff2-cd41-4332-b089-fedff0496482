"""
Structured Data API Endpoints for Emma Studio
Provides Schema.org JSON-LD generation for better LLM discoverability
"""

import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse, HTMLResponse
from pydantic import BaseModel, Field

from app.services.structured_data_service import structured_data_service

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic models for request/response
class ArticleSchemaRequest(BaseModel):
    """Request model for Article schema generation."""
    title: str = Field(..., description="Article title")
    content: str = Field(..., description="Article content")
    description: Optional[str] = Field(None, description="Article description")
    keywords: Optional[List[str]] = Field(None, description="Article keywords")
    author: str = Field("Emma Studio AI", description="Article author")
    published_date: Optional[datetime] = Field(None, description="Publication date")
    modified_date: Optional[datetime] = Field(None, description="Last modification date")
    url: Optional[str] = Field(None, description="Article URL")
    image_url: Optional[str] = Field(None, description="Featured image URL")
    language: str = Field("es", description="Content language")

class FAQSchemaRequest(BaseModel):
    """Request model for FAQ schema generation."""
    questions_answers: List[Dict[str, str]] = Field(..., description="List of Q&A pairs")
    title: Optional[str] = Field(None, description="FAQ page title")
    url: Optional[str] = Field(None, description="FAQ page URL")

class BreadcrumbItem(BaseModel):
    """Breadcrumb item model."""
    name: str = Field(..., description="Breadcrumb name")
    url: Optional[str] = Field(None, description="Breadcrumb URL")

class BreadcrumbSchemaRequest(BaseModel):
    """Request model for Breadcrumb schema generation."""
    breadcrumbs: List[BreadcrumbItem] = Field(..., description="List of breadcrumb items")

class ContentAnalysisRequest(BaseModel):
    """Request model for content analysis and schema generation."""
    content: str = Field(..., description="Content to analyze")
    title: Optional[str] = Field(None, description="Content title")
    url: Optional[str] = Field(None, description="Content URL")
    content_type: str = Field("article", description="Type of content")

class FAQExtractionRequest(BaseModel):
    """Request model for FAQ extraction."""
    content: str = Field(..., description="Content to analyze for FAQ pairs")

@router.post("/article")
async def generate_article_schema(request: ArticleSchemaRequest):
    """
    Generate Article schema markup.
    
    This endpoint creates Schema.org Article JSON-LD markup
    optimized for LLM discovery and citation.
    """
    try:
        schema = structured_data_service.generate_article_schema(
            title=request.title,
            content=request.content,
            description=request.description,
            keywords=request.keywords,
            author=request.author,
            published_date=request.published_date,
            modified_date=request.modified_date,
            url=request.url,
            image_url=request.image_url,
            language=request.language
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "schema": schema,
                "json_ld_script": structured_data_service.generate_json_ld_script(schema),
                "schema_type": "Article"
            }
        )
        
    except Exception as e:
        logger.error(f"Article schema generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Article schema generation failed: {str(e)}"
        )

@router.post("/faq")
async def generate_faq_schema(request: FAQSchemaRequest):
    """
    Generate FAQPage schema markup.
    
    This endpoint creates Schema.org FAQPage JSON-LD markup
    for better question-answer content discovery by LLMs.
    """
    try:
        schema = structured_data_service.generate_faq_schema(
            questions_answers=request.questions_answers,
            title=request.title,
            url=request.url
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "schema": schema,
                "json_ld_script": structured_data_service.generate_json_ld_script(schema),
                "schema_type": "FAQPage",
                "faq_count": len(request.questions_answers)
            }
        )
        
    except Exception as e:
        logger.error(f"FAQ schema generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"FAQ schema generation failed: {str(e)}"
        )

@router.get("/organization")
async def generate_organization_schema():
    """
    Generate Organization schema markup.
    
    This endpoint creates Schema.org Organization JSON-LD markup
    for Emma Studio company information.
    """
    try:
        schema = structured_data_service.generate_organization_schema()
        
        return JSONResponse(
            status_code=200,
            content={
                "schema": schema,
                "json_ld_script": structured_data_service.generate_json_ld_script(schema),
                "schema_type": "Organization"
            }
        )
        
    except Exception as e:
        logger.error(f"Organization schema generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Organization schema generation failed: {str(e)}"
        )

@router.get("/website")
async def generate_website_schema(
    site_name: Optional[str] = Query(None, description="Website name"),
    description: Optional[str] = Query(None, description="Website description"),
    url: Optional[str] = Query(None, description="Website URL")
):
    """
    Generate WebSite schema markup.
    
    This endpoint creates Schema.org WebSite JSON-LD markup
    for the main website structure.
    """
    try:
        schema = structured_data_service.generate_website_schema(
            site_name=site_name,
            description=description,
            url=url
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "schema": schema,
                "json_ld_script": structured_data_service.generate_json_ld_script(schema),
                "schema_type": "WebSite"
            }
        )
        
    except Exception as e:
        logger.error(f"Website schema generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Website schema generation failed: {str(e)}"
        )

@router.post("/breadcrumb")
async def generate_breadcrumb_schema(request: BreadcrumbSchemaRequest):
    """
    Generate BreadcrumbList schema markup.
    
    This endpoint creates Schema.org BreadcrumbList JSON-LD markup
    for navigation structure.
    """
    try:
        breadcrumbs = [item.dict() for item in request.breadcrumbs]
        schema = structured_data_service.generate_breadcrumb_schema(breadcrumbs)
        
        return JSONResponse(
            status_code=200,
            content={
                "schema": schema,
                "json_ld_script": structured_data_service.generate_json_ld_script(schema),
                "schema_type": "BreadcrumbList",
                "breadcrumb_count": len(breadcrumbs)
            }
        )
        
    except Exception as e:
        logger.error(f"Breadcrumb schema generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Breadcrumb schema generation failed: {str(e)}"
        )

@router.get("/software-application")
async def generate_software_application_schema(
    app_name: str = Query("Emma Studio", description="Application name"),
    description: Optional[str] = Query(None, description="Application description"),
    features: Optional[List[str]] = Query(None, description="Application features")
):
    """
    Generate SoftwareApplication schema markup.
    
    This endpoint creates Schema.org SoftwareApplication JSON-LD markup
    for the Emma Studio platform.
    """
    try:
        schema = structured_data_service.generate_software_application_schema(
            app_name=app_name,
            description=description,
            features=features
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "schema": schema,
                "json_ld_script": structured_data_service.generate_json_ld_script(schema),
                "schema_type": "SoftwareApplication"
            }
        )
        
    except Exception as e:
        logger.error(f"SoftwareApplication schema generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"SoftwareApplication schema generation failed: {str(e)}"
        )

@router.post("/analyze-content")
async def analyze_content_for_schemas(request: ContentAnalysisRequest):
    """
    Analyze content and generate appropriate schemas.
    
    This endpoint analyzes content and automatically generates
    the most appropriate Schema.org markup based on content type.
    """
    try:
        schemas = []
        
        # Generate Article schema
        article_schema = structured_data_service.generate_article_schema(
            title=request.title or "Contenido Generado",
            content=request.content,
            url=request.url
        )
        if article_schema:
            schemas.append(article_schema)
        
        # Try to extract FAQ from content
        faq_pairs = structured_data_service.extract_faq_from_content(request.content)
        if faq_pairs:
            faq_schema = structured_data_service.generate_faq_schema(
                questions_answers=faq_pairs,
                url=request.url
            )
            if faq_schema:
                schemas.append(faq_schema)
        
        # Combine schemas
        combined_schemas = structured_data_service.combine_schemas(*schemas)
        
        return JSONResponse(
            status_code=200,
            content={
                "schemas": combined_schemas,
                "json_ld_script": structured_data_service.generate_json_ld_script(combined_schemas),
                "detected_types": [s.get("@type") for s in combined_schemas],
                "faq_pairs_found": len(faq_pairs),
                "total_schemas": len(combined_schemas)
            }
        )
        
    except Exception as e:
        logger.error(f"Content analysis for schemas failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Content analysis failed: {str(e)}"
        )

@router.post("/extract-faq")
async def extract_faq_from_content(request: FAQExtractionRequest):
    """
    Extract FAQ pairs from content.
    
    This endpoint analyzes content and extracts question-answer pairs
    that can be used for FAQ schema generation.
    """
    try:
        faq_pairs = structured_data_service.extract_faq_from_content(request.content)
        
        return JSONResponse(
            status_code=200,
            content={
                "faq_pairs": faq_pairs,
                "total_pairs": len(faq_pairs),
                "has_faq_content": len(faq_pairs) > 0
            }
        )
        
    except Exception as e:
        logger.error(f"FAQ extraction failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"FAQ extraction failed: {str(e)}"
        )

@router.get("/combined")
async def generate_combined_schemas(
    include_organization: bool = Query(True, description="Include Organization schema"),
    include_website: bool = Query(True, description="Include WebSite schema"),
    include_software: bool = Query(True, description="Include SoftwareApplication schema")
):
    """
    Generate combined schemas for the main website.
    
    This endpoint generates multiple Schema.org markups that should
    be included on the main website pages for comprehensive LLM discovery.
    """
    try:
        schemas = []
        
        if include_organization:
            org_schema = structured_data_service.generate_organization_schema()
            if org_schema:
                schemas.append(org_schema)
        
        if include_website:
            website_schema = structured_data_service.generate_website_schema()
            if website_schema:
                schemas.append(website_schema)
        
        if include_software:
            app_schema = structured_data_service.generate_software_application_schema()
            if app_schema:
                schemas.append(app_schema)
        
        combined_schemas = structured_data_service.combine_schemas(*schemas)
        
        return JSONResponse(
            status_code=200,
            content={
                "schemas": combined_schemas,
                "json_ld_script": structured_data_service.generate_json_ld_script(combined_schemas),
                "included_types": [s.get("@type") for s in combined_schemas],
                "total_schemas": len(combined_schemas)
            }
        )
        
    except Exception as e:
        logger.error(f"Combined schemas generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Combined schemas generation failed: {str(e)}"
        )

@router.get("/json-ld/{schema_type}")
async def get_json_ld_script(
    schema_type: str,
    title: Optional[str] = Query(None),
    content: Optional[str] = Query(None),
    url: Optional[str] = Query(None)
):
    """
    Get JSON-LD script tag for specific schema type.
    
    This endpoint returns ready-to-use HTML script tags
    with JSON-LD content for direct insertion into web pages.
    """
    try:
        schema = None
        
        if schema_type.lower() == "organization":
            schema = structured_data_service.generate_organization_schema()
        elif schema_type.lower() == "website":
            schema = structured_data_service.generate_website_schema()
        elif schema_type.lower() == "software":
            schema = structured_data_service.generate_software_application_schema()
        elif schema_type.lower() == "article" and title and content:
            schema = structured_data_service.generate_article_schema(
                title=title,
                content=content,
                url=url
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported schema type: {schema_type}"
            )
        
        if not schema:
            raise HTTPException(
                status_code=500,
                detail="Failed to generate schema"
            )
        
        json_ld_script = structured_data_service.generate_json_ld_script(schema)
        
        return HTMLResponse(
            content=json_ld_script,
            media_type="text/html"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"JSON-LD script generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"JSON-LD script generation failed: {str(e)}"
        )

@router.get("/status")
async def get_structured_data_status():
    """
    Get the current status of the structured data service.
    
    Returns information about available schema types and service health.
    """
    try:
        return JSONResponse(
            status_code=200,
            content={
                "service": "Structured Data Generation API",
                "version": "1.0.0",
                "status": "healthy",
                "supported_schemas": [
                    "Article",
                    "FAQPage", 
                    "Organization",
                    "WebSite",
                    "BreadcrumbList",
                    "SoftwareApplication"
                ],
                "features": {
                    "json_ld_generation": True,
                    "content_analysis": True,
                    "faq_extraction": True,
                    "combined_schemas": True,
                    "html_script_tags": True
                },
                "organization_info": structured_data_service.organization_info
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get structured data status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get status: {str(e)}"
        )
