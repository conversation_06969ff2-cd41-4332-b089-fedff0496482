"""
SEO Metadata Generation API Endpoints for Emma Studio
Provides automatic SEO metadata generation using transformer models
"""

import logging
import time
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, BackgroundTasks, Query
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field

from app.core.config import settings
from app.services.seo_metadata_service import seo_metadata_service

logger = logging.getLogger(__name__)

router = APIRouter()

# Pydantic models for request/response
class SEOMetadataRequest(BaseModel):
    """Request model for SEO metadata generation."""
    content: str = Field(..., description="Content text to analyze", min_length=50)
    title: Optional[str] = Field(None, description="Existing title (optional)")
    target_keywords: Optional[List[str]] = Field(None, description="Target keywords for optimization")
    language: str = Field("es", description="Target language", pattern="^(es|en|fr|de|it|pt)$")
    content_type: str = Field("article", description="Type of content")

class SEOMetadataResponse(BaseModel):
    """Response model for SEO metadata generation."""
    title: str = Field(..., description="Optimized title")
    meta_description: str = Field(..., description="Optimized meta description")
    keywords: List[str] = Field(..., description="Extracted/optimized keywords")
    suggested_headings: List[Dict[str, str]] = Field(..., description="Suggested heading structure")
    schema_markup: Dict[str, Any] = Field(..., description="Schema.org JSON-LD markup")
    content_analysis: Dict[str, Any] = Field(..., description="Content structure analysis")
    seo_score: Dict[str, Any] = Field(..., description="SEO optimization score")
    optimization_suggestions: List[str] = Field(..., description="SEO improvement suggestions")
    generated_at: str = Field(..., description="Generation timestamp")
    language: str = Field(..., description="Content language")
    processing_time_ms: float = Field(..., description="Processing time in milliseconds")

class BulkSEORequest(BaseModel):
    """Request model for bulk SEO metadata generation."""
    contents: List[Dict[str, Any]] = Field(..., description="List of content items to process")
    language: str = Field("es", description="Target language")

class SEOAnalysisRequest(BaseModel):
    """Request model for SEO content analysis only."""
    content: str = Field(..., description="Content to analyze")
    title: Optional[str] = Field(None, description="Content title")
    language: str = Field("es", description="Content language")

@router.post("/generate-metadata", response_model=SEOMetadataResponse)
async def generate_seo_metadata(request: SEOMetadataRequest):
    """
    Generate comprehensive SEO metadata for content.
    
    This endpoint uses AI models (Gemini + BART) to automatically generate:
    - Optimized titles and meta descriptions
    - Relevant keywords extraction
    - Schema.org structured data
    - Content structure analysis
    - SEO optimization score and suggestions
    """
    try:
        start_time = time.time()
        
        # Ensure service is initialized
        if not seo_metadata_service.initialized:
            await seo_metadata_service.initialize()
        
        # Generate SEO metadata
        metadata = await seo_metadata_service.generate_seo_metadata(
            content=request.content,
            title=request.title,
            target_keywords=request.target_keywords,
            language=request.language
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        # Add processing time to response
        metadata["processing_time_ms"] = processing_time
        
        return SEOMetadataResponse(**metadata)
        
    except Exception as e:
        logger.error(f"SEO metadata generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"SEO metadata generation failed: {str(e)}"
        )

@router.post("/analyze-content")
async def analyze_seo_content(request: SEOAnalysisRequest):
    """
    Analyze content for SEO optimization without generating new metadata.
    
    This endpoint provides SEO analysis and scoring for existing content.
    """
    try:
        start_time = time.time()
        
        # Ensure service is initialized
        if not seo_metadata_service.initialized:
            await seo_metadata_service.initialize()
        
        # Analyze content structure
        content_analysis = await seo_metadata_service._analyze_content_structure(request.content)
        
        # Calculate SEO score
        seo_score = seo_metadata_service._calculate_seo_score(
            request.title or "",
            "",  # No description for analysis-only
            [],  # No keywords for analysis-only
            request.content
        )
        
        # Generate optimization suggestions
        suggestions = seo_metadata_service._generate_optimization_suggestions(
            request.title or "",
            "",
            [],
            request.content
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        return JSONResponse(
            status_code=200,
            content={
                "content_analysis": content_analysis,
                "seo_score": seo_score,
                "optimization_suggestions": suggestions,
                "language": request.language,
                "processing_time_ms": processing_time
            }
        )
        
    except Exception as e:
        logger.error(f"SEO content analysis failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"SEO content analysis failed: {str(e)}"
        )

@router.post("/bulk-generate")
async def bulk_generate_seo_metadata(
    request: BulkSEORequest,
    background_tasks: BackgroundTasks
):
    """
    Generate SEO metadata for multiple content items in bulk.
    
    This endpoint processes multiple content items and returns
    SEO metadata for each one. Large batches are processed in background.
    """
    try:
        if len(request.contents) > 10:
            # Process large batches in background
            background_tasks.add_task(
                _process_bulk_seo_metadata,
                request.contents,
                request.language
            )
            
            return JSONResponse(
                status_code=202,
                content={
                    "message": f"Bulk SEO metadata generation started for {len(request.contents)} items",
                    "status": "processing",
                    "items_count": len(request.contents)
                }
            )
        
        # Process small batches immediately
        results = []
        for i, content_item in enumerate(request.contents):
            try:
                metadata = await seo_metadata_service.generate_seo_metadata(
                    content=content_item.get("content", ""),
                    title=content_item.get("title"),
                    target_keywords=content_item.get("keywords"),
                    language=request.language
                )
                
                results.append({
                    "index": i,
                    "content_id": content_item.get("id", f"item_{i}"),
                    "metadata": metadata,
                    "status": "success"
                })
                
            except Exception as e:
                logger.error(f"Failed to process item {i}: {e}")
                results.append({
                    "index": i,
                    "content_id": content_item.get("id", f"item_{i}"),
                    "error": str(e),
                    "status": "error"
                })
        
        return JSONResponse(
            status_code=200,
            content={
                "results": results,
                "total_items": len(request.contents),
                "successful_items": len([r for r in results if r["status"] == "success"]),
                "failed_items": len([r for r in results if r["status"] == "error"])
            }
        )
        
    except Exception as e:
        logger.error(f"Bulk SEO metadata generation failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Bulk processing failed: {str(e)}"
        )

@router.get("/keywords/suggest")
async def suggest_keywords(
    content: str = Query(..., description="Content to analyze for keywords"),
    count: int = Query(10, description="Number of keywords to suggest", ge=1, le=20),
    language: str = Query("es", description="Content language")
):
    """
    Suggest SEO keywords for given content.
    
    This endpoint analyzes content and suggests relevant keywords
    for SEO optimization.
    """
    try:
        # Ensure service is initialized
        if not seo_metadata_service.initialized:
            await seo_metadata_service.initialize()
        
        # Extract keywords
        keywords = await seo_metadata_service._extract_keywords(
            content, None, language
        )
        
        return JSONResponse(
            status_code=200,
            content={
                "suggested_keywords": keywords[:count],
                "total_suggestions": len(keywords),
                "language": language
            }
        )
        
    except Exception as e:
        logger.error(f"Keyword suggestion failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Keyword suggestion failed: {str(e)}"
        )

@router.get("/status")
async def get_seo_metadata_status():
    """
    Get the current status of the SEO metadata service.
    
    Returns information about the service health and capabilities.
    """
    try:
        is_initialized = seo_metadata_service.initialized
        has_gemini = seo_metadata_service.gemini_model is not None
        has_summarization = seo_metadata_service.summarization_pipeline is not None
        
        return JSONResponse(
            status_code=200,
            content={
                "service": "SEO Metadata Generation API",
                "version": "1.0.0",
                "status": "healthy" if is_initialized else "initializing",
                "capabilities": {
                    "gemini_ai": has_gemini,
                    "summarization": has_summarization,
                    "keyword_extraction": True,
                    "schema_generation": True,
                    "bulk_processing": True
                },
                "features": {
                    "title_optimization": True,
                    "meta_description_generation": True,
                    "keyword_extraction": True,
                    "content_analysis": True,
                    "seo_scoring": True,
                    "schema_markup": True,
                    "multi_language": True
                },
                "supported_languages": ["es", "en", "fr", "de", "it", "pt"],
                "limits": {
                    "max_title_length": seo_metadata_service.TITLE_MAX_LENGTH,
                    "max_description_length": seo_metadata_service.DESCRIPTION_MAX_LENGTH,
                    "max_keywords_count": seo_metadata_service.KEYWORDS_MAX_COUNT
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get SEO metadata status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get status: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """Health check endpoint for SEO metadata service."""
    try:
        # Basic health check
        if not seo_metadata_service.initialized:
            await seo_metadata_service.initialize()
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "healthy",
                "service": "seo_metadata",
                "timestamp": time.time()
            }
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "seo_metadata",
                "error": str(e)
            }
        )

async def _process_bulk_seo_metadata(contents: List[Dict[str, Any]], language: str):
    """Background task for processing bulk SEO metadata generation."""
    try:
        logger.info(f"Starting bulk SEO metadata processing for {len(contents)} items")
        
        for i, content_item in enumerate(contents):
            try:
                await seo_metadata_service.generate_seo_metadata(
                    content=content_item.get("content", ""),
                    title=content_item.get("title"),
                    target_keywords=content_item.get("keywords"),
                    language=language
                )
                
                if i % 10 == 0:  # Log progress every 10 items
                    logger.info(f"Processed {i+1}/{len(contents)} items")
                    
            except Exception as e:
                logger.error(f"Failed to process bulk item {i}: {e}")
        
        logger.info(f"Completed bulk SEO metadata processing for {len(contents)} items")
        
    except Exception as e:
        logger.error(f"Bulk SEO metadata processing failed: {e}")
