"""
API endpoints for text overlay composition - Two-layer system
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.services.text_overlay_service import text_overlay_service

logger = logging.getLogger(__name__)
router = APIRouter()


class TextOverlayRequest(BaseModel):
    background_image_url: str
    text: str
    overlay_config: Dict[str, Any] = {}
    dimensions: Dict[str, int] = {"width": 1080, "height": 1080}


class TextOverlayResponse(BaseModel):
    success: bool
    image_data: str = None
    format: str = None
    dimensions: Dict[str, int] = None
    metadata: Dict[str, Any] = None
    error: str = None


@router.post("/compose", response_model=TextOverlayResponse)
async def compose_text_overlay(request: TextOverlayRequest):
    """
    Compose text overlay on a clean background image.
    
    This endpoint implements the two-layer system:
    1. Takes a clean background image (no text)
    2. Overlays text with professional typography
    3. Returns the composed image
    
    Args:
        request: TextOverlayRequest with background URL, text, and styling
        
    Returns:
        TextOverlayResponse with composed image data
    """
    try:
        logger.info(f"🎨 Starting text overlay composition")
        logger.info(f"📝 Text: '{request.text[:50]}...'")
        logger.info(f"🖼️ Background: {request.background_image_url[:50]}...")
        
        # Validate request
        if not request.background_image_url:
            raise HTTPException(status_code=400, detail="Background image URL is required")
        
        if not request.text:
            raise HTTPException(status_code=400, detail="Text is required")
        
        # Set default overlay configuration
        default_config = {
            "font_family": "Inter, sans-serif",
            "font_weight": "bold",
            "font_size": "auto",
            "color": "#FFFFFF",
            "position": "center",
            "background_opacity": 0.7,
            "text_shadow": True
        }
        
        # Merge with user config
        overlay_config = {**default_config, **request.overlay_config}
        
        # Compose text overlay
        result = await text_overlay_service.compose_text_overlay(
            background_image_url=request.background_image_url,
            text=request.text,
            overlay_config=overlay_config,
            dimensions=request.dimensions
        )
        
        if not result.get("success"):
            logger.error(f"❌ Text overlay composition failed: {result.get('error')}")
            raise HTTPException(status_code=500, detail=result.get("error", "Composition failed"))
        
        logger.info("✅ Text overlay composition completed successfully")
        
        return TextOverlayResponse(
            success=True,
            image_data=result["image_data"],
            format=result["format"],
            dimensions=result["dimensions"],
            metadata=result["metadata"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Critical error in text overlay composition: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/preview")
async def preview_text_overlay(request: TextOverlayRequest):
    """
    Preview text overlay without full composition (faster response).
    
    Returns positioning and styling information for frontend preview.
    """
    try:
        logger.info(f"👁️ Generating text overlay preview")
        
        # Calculate preview information
        dimensions = request.dimensions
        text = request.text
        config = request.overlay_config
        
        # Calculate font size
        width = dimensions.get("width", 1080)
        height = dimensions.get("height", 1080)
        
        # Simple font size calculation
        base_size = min(width, height) // 15
        text_length = len(text)
        
        if text_length <= 20:
            font_size = int(base_size * 1.2)
        elif text_length <= 40:
            font_size = base_size
        elif text_length <= 60:
            font_size = int(base_size * 0.8)
        else:
            font_size = int(base_size * 0.6)
        
        # Ensure bounds
        font_size = max(24, min(font_size, min(width, height) // 8))
        
        # Calculate position
        position = config.get("position", "center")
        if position == "center":
            x = width // 2
            y = height // 2
        elif position == "top":
            x = width // 2
            y = height // 6
        elif position == "bottom":
            x = width // 2
            y = height - height // 6
        else:
            x = width // 2
            y = height // 2
        
        preview_data = {
            "success": True,
            "preview": {
                "font_size": font_size,
                "position": {"x": x, "y": y},
                "text_color": config.get("color", "#FFFFFF"),
                "background_opacity": config.get("background_opacity", 0.7),
                "text_shadow": config.get("text_shadow", True),
                "estimated_width": len(text) * (font_size * 0.6),
                "estimated_height": font_size
            },
            "metadata": {
                "text_length": len(text),
                "dimensions": dimensions,
                "preview_generated": True
            }
        }
        
        logger.info("✅ Text overlay preview generated")
        return preview_data
        
    except Exception as e:
        logger.error(f"❌ Error generating text overlay preview: {e}")
        raise HTTPException(status_code=500, detail=f"Preview generation failed: {str(e)}")


@router.get("/fonts")
async def get_available_fonts():
    """
    Get list of available fonts for text overlay.
    
    Returns:
        List of available font families and weights
    """
    try:
        available_fonts = {
            "families": [
                {
                    "name": "Inter",
                    "display_name": "Inter (Recommended)",
                    "weights": ["regular", "medium", "bold", "black"],
                    "recommended": True
                },
                {
                    "name": "Arial",
                    "display_name": "Arial",
                    "weights": ["regular", "bold"],
                    "recommended": False
                },
                {
                    "name": "Helvetica",
                    "display_name": "Helvetica",
                    "weights": ["regular", "bold"],
                    "recommended": False
                },
                {
                    "name": "Roboto",
                    "display_name": "Roboto",
                    "weights": ["light", "regular", "medium", "bold"],
                    "recommended": True
                }
            ],
            "default": {
                "family": "Inter",
                "weight": "bold",
                "size": "auto"
            },
            "positions": [
                {"value": "center", "label": "Centro"},
                {"value": "top", "label": "Arriba"},
                {"value": "bottom", "label": "Abajo"}
            ]
        }
        
        return available_fonts
        
    except Exception as e:
        logger.error(f"❌ Error getting available fonts: {e}")
        raise HTTPException(status_code=500, detail="Failed to get font information")


@router.get("/health")
async def health_check():
    """
    Health check for text overlay service.
    """
    try:
        # Check if service is available
        service_available = text_overlay_service is not None
        
        return {
            "status": "healthy" if service_available else "unhealthy",
            "service": "Text Overlay Service",
            "features": {
                "text_composition": True,
                "font_support": True,
                "position_control": True,
                "preview_generation": True
            },
            "version": "1.0.0"
        }
        
    except Exception as e:
        logger.error(f"❌ Text overlay health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }
