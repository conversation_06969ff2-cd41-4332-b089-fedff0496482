"""API endpoints for user-defined color palette management."""

import logging
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field, validator

from app.core.auth import get_current_user_from_token
from app.core.supabase import SupabaseService, get_supabase_service

logger = logging.getLogger(__name__)
router = APIRouter()


class PaletteCreate(BaseModel):
    """Schema for creating a new color palette."""
    name: str = Field(..., min_length=1, max_length=100, description="Palette name")
    colors: List[str] = Field(..., min_items=1, max_items=20, description="Array of hex color codes")
    description: Optional[str] = Field(None, max_length=500, description="Optional palette description")
    tags: Optional[List[str]] = Field(default_factory=list, description="Optional tags for categorization")
    is_favorite: Optional[bool] = Field(False, description="Whether this palette is marked as favorite")

    @validator('colors')
    def validate_hex_colors(cls, v):
        """Validate that all colors are valid hex codes."""
        for color in v:
            if not isinstance(color, str):
                raise ValueError(f"Color must be a string, got {type(color)}")
            
            # Remove # if present and validate hex format
            hex_color = color.lstrip('#')
            if len(hex_color) not in [3, 6]:
                raise ValueError(f"Invalid hex color format: {color}")
            
            try:
                int(hex_color, 16)
            except ValueError:
                raise ValueError(f"Invalid hex color: {color}")
                
            # Ensure color starts with #
            if not color.startswith('#'):
                v[v.index(color)] = f"#{color}"
        
        return v

    @validator('tags')
    def validate_tags(cls, v):
        """Validate tags array."""
        if v and len(v) > 10:
            raise ValueError("Maximum 10 tags allowed")
        return v or []


class PaletteUpdate(BaseModel):
    """Schema for updating an existing color palette."""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Palette name")
    colors: Optional[List[str]] = Field(None, min_items=1, max_items=20, description="Array of hex color codes")
    description: Optional[str] = Field(None, max_length=500, description="Palette description")
    tags: Optional[List[str]] = Field(None, description="Tags for categorization")
    is_favorite: Optional[bool] = Field(None, description="Favorite status")

    @validator('colors')
    def validate_hex_colors(cls, v):
        """Validate that all colors are valid hex codes."""
        if v is None:
            return v
            
        for color in v:
            if not isinstance(color, str):
                raise ValueError(f"Color must be a string, got {type(color)}")
            
            # Remove # if present and validate hex format
            hex_color = color.lstrip('#')
            if len(hex_color) not in [3, 6]:
                raise ValueError(f"Invalid hex color format: {color}")
            
            try:
                int(hex_color, 16)
            except ValueError:
                raise ValueError(f"Invalid hex color: {color}")
                
            # Ensure color starts with #
            if not color.startswith('#'):
                v[v.index(color)] = f"#{color}"
        
        return v

    @validator('tags')
    def validate_tags(cls, v):
        """Validate tags array."""
        if v and len(v) > 10:
            raise ValueError("Maximum 10 tags allowed")
        return v


@router.post("/palettes")
async def create_palette(
    palette_data: PaletteCreate,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Create a new color palette for the authenticated user.
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to save palettes"
        )

    try:
        # Convert Pydantic model to dict
        palette_dict = palette_data.dict()
        
        # Save palette to database (using direct table insert like Visual Complexity Analyzer)
        saved_palette = await supabase_service.save_user_palette(
            user_id=current_user["user_id"],
            palette_data=palette_dict
        )

        if not saved_palette:
            raise HTTPException(
                status_code=500,
                detail="Failed to save palette to database"
            )

        logger.info(f"Created palette '{palette_data.name}' for user {current_user['user_id']}")

        return {
            "success": True,
            "message": "Palette created successfully",
            "palette": saved_palette
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating palette: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create palette: {str(e)}"
        )


@router.get("/palettes")
async def get_user_palettes(
    limit: int = Query(50, ge=1, le=100, description="Maximum number of palettes to return"),
    offset: int = Query(0, ge=0, description="Number of palettes to skip"),
    is_favorite: Optional[bool] = Query(None, description="Filter by favorite status"),
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Get all color palettes for the authenticated user.
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to access saved palettes"
        )

    try:
        palettes = await supabase_service.get_user_palettes(
            user_id=current_user["user_id"],
            limit=limit,
            offset=offset
        )

        # Filter by favorite status if specified
        if is_favorite is not None:
            palettes = [p for p in palettes if p.get("is_favorite") == is_favorite]

        return {
            "success": True,
            "palettes": palettes,
            "count": len(palettes),
            "user_id": current_user["user_id"]
        }

    except Exception as e:
        logger.error(f"Error fetching user palettes: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch palettes: {str(e)}"
        )


@router.get("/palettes/{palette_id}")
async def get_palette_by_id(
    palette_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Get a specific color palette by ID.
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to access saved palettes"
        )

    try:
        palette = await supabase_service.get_palette_by_id(
            palette_id=palette_id,
            user_id=current_user["user_id"]
        )

        if not palette:
            raise HTTPException(
                status_code=404,
                detail="Palette not found or access denied"
            )

        return {
            "success": True,
            "palette": palette
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching palette {palette_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch palette: {str(e)}"
        )


@router.put("/palettes/{palette_id}")
async def update_palette(
    palette_id: str,
    palette_data: PaletteUpdate,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Update an existing color palette.
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to update palettes"
        )

    try:
        # Convert Pydantic model to dict, excluding None values
        update_dict = palette_data.dict(exclude_none=True)
        
        if not update_dict:
            raise HTTPException(
                status_code=400,
                detail="No update data provided"
            )

        # Update palette in database
        updated_palette = await supabase_service.update_user_palette(
            palette_id=palette_id,
            user_id=current_user["user_id"],
            palette_data=update_dict
        )

        if not updated_palette:
            raise HTTPException(
                status_code=404,
                detail="Palette not found or access denied"
            )

        logger.info(f"Updated palette {palette_id} for user {current_user['user_id']}")

        return {
            "success": True,
            "message": "Palette updated successfully",
            "palette": updated_palette
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating palette {palette_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update palette: {str(e)}"
        )


@router.delete("/palettes/{palette_id}")
async def delete_palette(
    palette_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Delete a color palette.
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to delete palettes"
        )

    try:
        success = await supabase_service.delete_user_palette(
            palette_id=palette_id,
            user_id=current_user["user_id"]
        )

        if not success:
            raise HTTPException(
                status_code=404,
                detail="Palette not found or access denied"
            )

        logger.info(f"Deleted palette {palette_id} for user {current_user['user_id']}")

        return {
            "success": True,
            "message": "Palette deleted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting palette {palette_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete palette: {str(e)}"
        )


@router.patch("/palettes/{palette_id}/favorite")
async def toggle_palette_favorite(
    palette_id: str,
    is_favorite: bool,
    current_user: Dict[str, Any] = Depends(get_current_user_from_token),
    supabase_service: SupabaseService = Depends(get_supabase_service)
) -> Dict[str, Any]:
    """
    Toggle the favorite status of a palette.
    """
    if current_user["user_id"] == "anonymous":
        raise HTTPException(
            status_code=401,
            detail="Authentication required to update palette favorites"
        )

    try:
        success = await supabase_service.update_palette_favorite(
            palette_id=palette_id,
            user_id=current_user["user_id"],
            is_favorite=is_favorite
        )

        if not success:
            raise HTTPException(
                status_code=404,
                detail="Palette not found or access denied"
            )

        logger.info(f"Updated favorite status for palette {palette_id} to {is_favorite}")

        return {
            "success": True,
            "message": f"Palette {'added to' if is_favorite else 'removed from'} favorites",
            "is_favorite": is_favorite
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating favorite status for palette {palette_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update favorite status: {str(e)}"
        )
