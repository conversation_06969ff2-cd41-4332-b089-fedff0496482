"""
Standardized Error Response System for Emma Studio
Provides consistent error formatting and HTTP status codes across all endpoints.
"""

import logging
import uuid
from typing import Dict, Any, Optional, Union
from fastapi import HTTPException
from fastapi.responses import J<PERSON>NResponse

logger = logging.getLogger(__name__)

class ErrorCode:
    """Standard error codes for Emma Studio."""
    
    # Validation errors (400)
    VALIDATION_ERROR = "validation_error"
    INVALID_INPUT = "invalid_input"
    MISSING_REQUIRED_FIELD = "missing_required_field"
    INVALID_FILE_FORMAT = "invalid_file_format"
    FILE_TOO_LARGE = "file_too_large"
    
    # Authentication/Authorization errors (401/403)
    AUTHENTICATION_FAILED = "authentication_failed"
    API_KEY_INVALID = "api_key_invalid"
    API_KEY_MISSING = "api_key_missing"
    INSUFFICIENT_PERMISSIONS = "insufficient_permissions"
    
    # Not found errors (404)
    RESOURCE_NOT_FOUND = "resource_not_found"
    ENDPOINT_NOT_FOUND = "endpoint_not_found"
    SERVICE_NOT_FOUND = "service_not_found"
    
    # Rate limiting (429)
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    API_QUOTA_EXCEEDED = "api_quota_exceeded"
    
    # External service errors (503)
    EXTERNAL_SERVICE_UNAVAILABLE = "external_service_unavailable"
    EXTERNAL_SERVICE_TIMEOUT = "external_service_timeout"
    EXTERNAL_SERVICE_ERROR = "external_service_error"
    
    # Internal server errors (500)
    INTERNAL_SERVER_ERROR = "internal_server_error"
    DATABASE_ERROR = "database_error"
    CONFIGURATION_ERROR = "configuration_error"
    PROCESSING_ERROR = "processing_error"

class StandardErrorResponse:
    """Standardized error response builder."""
    
    @staticmethod
    def create_error_response(
        status_code: int,
        error_code: str,
        message: str,
        details: Optional[str] = None,
        request_id: Optional[str] = None,
        field_errors: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Create a standardized error response.
        
        Args:
            status_code: HTTP status code
            error_code: Standard error code from ErrorCode class
            message: User-friendly error message in Spanish
            details: Technical details for debugging (optional)
            request_id: Request ID for tracking (optional)
            field_errors: Field-specific validation errors (optional)
        
        Returns:
            Standardized error response dictionary
        """
        if not request_id:
            request_id = str(uuid.uuid4())[:8]
        
        error_response = {
            "status": "error",
            "error": {
                "code": error_code,
                "message": message,
                "request_id": request_id
            }
        }
        
        if details:
            error_response["error"]["details"] = details
        
        if field_errors:
            error_response["error"]["field_errors"] = field_errors
        
        return error_response
    
    @staticmethod
    def validation_error(
        message: str = "Los datos proporcionados no son válidos",
        details: Optional[str] = None,
        field_errors: Optional[Dict[str, str]] = None,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """Create a validation error response (400)."""
        return JSONResponse(
            status_code=400,
            content=StandardErrorResponse.create_error_response(
                status_code=400,
                error_code=ErrorCode.VALIDATION_ERROR,
                message=message,
                details=details,
                request_id=request_id,
                field_errors=field_errors
            )
        )
    
    @staticmethod
    def authentication_error(
        message: str = "Autenticación requerida",
        details: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """Create an authentication error response (401)."""
        return JSONResponse(
            status_code=401,
            content=StandardErrorResponse.create_error_response(
                status_code=401,
                error_code=ErrorCode.AUTHENTICATION_FAILED,
                message=message,
                details=details,
                request_id=request_id
            )
        )
    
    @staticmethod
    def not_found_error(
        message: str = "El recurso solicitado no fue encontrado",
        details: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """Create a not found error response (404)."""
        return JSONResponse(
            status_code=404,
            content=StandardErrorResponse.create_error_response(
                status_code=404,
                error_code=ErrorCode.RESOURCE_NOT_FOUND,
                message=message,
                details=details,
                request_id=request_id
            )
        )
    
    @staticmethod
    def rate_limit_error(
        message: str = "Límite de solicitudes excedido. Inténtalo de nuevo más tarde",
        details: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """Create a rate limit error response (429)."""
        return JSONResponse(
            status_code=429,
            content=StandardErrorResponse.create_error_response(
                status_code=429,
                error_code=ErrorCode.RATE_LIMIT_EXCEEDED,
                message=message,
                details=details,
                request_id=request_id
            )
        )
    
    @staticmethod
    def external_service_error(
        service_name: str,
        message: Optional[str] = None,
        details: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """Create an external service error response (503)."""
        if not message:
            message = f"El servicio {service_name} no está disponible temporalmente. Inténtalo de nuevo más tarde"
        
        return JSONResponse(
            status_code=503,
            content=StandardErrorResponse.create_error_response(
                status_code=503,
                error_code=ErrorCode.EXTERNAL_SERVICE_UNAVAILABLE,
                message=message,
                details=details,
                request_id=request_id
            )
        )
    
    @staticmethod
    def internal_server_error(
        message: str = "Error interno del servidor. Por favor contacta al soporte técnico",
        details: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """Create an internal server error response (500)."""
        return JSONResponse(
            status_code=500,
            content=StandardErrorResponse.create_error_response(
                status_code=500,
                error_code=ErrorCode.INTERNAL_SERVER_ERROR,
                message=message,
                details=details,
                request_id=request_id
            )
        )

def create_http_exception(
    status_code: int,
    error_code: str,
    message: str,
    details: Optional[str] = None,
    request_id: Optional[str] = None
) -> HTTPException:
    """
    Create an HTTPException with standardized error format.
    
    This is useful for raising exceptions that will be caught by
    the global exception handler and formatted consistently.
    """
    error_detail = StandardErrorResponse.create_error_response(
        status_code=status_code,
        error_code=error_code,
        message=message,
        details=details,
        request_id=request_id
    )
    
    return HTTPException(status_code=status_code, detail=error_detail)

# Convenience functions for common error types
def validation_exception(
    message: str = "Los datos proporcionados no son válidos",
    details: Optional[str] = None,
    field_errors: Optional[Dict[str, str]] = None,
    request_id: Optional[str] = None
) -> HTTPException:
    """Create a validation HTTPException."""
    error_detail = StandardErrorResponse.create_error_response(
        status_code=400,
        error_code=ErrorCode.VALIDATION_ERROR,
        message=message,
        details=details,
        request_id=request_id,
        field_errors=field_errors
    )
    return HTTPException(status_code=400, detail=error_detail)

def external_service_exception(
    service_name: str,
    message: Optional[str] = None,
    details: Optional[str] = None,
    request_id: Optional[str] = None
) -> HTTPException:
    """Create an external service HTTPException."""
    if not message:
        message = f"El servicio {service_name} no está disponible temporalmente"
    
    error_detail = StandardErrorResponse.create_error_response(
        status_code=503,
        error_code=ErrorCode.EXTERNAL_SERVICE_UNAVAILABLE,
        message=message,
        details=details,
        request_id=request_id
    )
    return HTTPException(status_code=503, detail=error_detail)

def internal_server_exception(
    message: str = "Error interno del servidor",
    details: Optional[str] = None,
    request_id: Optional[str] = None
) -> HTTPException:
    """Create an internal server HTTPException."""
    error_detail = StandardErrorResponse.create_error_response(
        status_code=500,
        error_code=ErrorCode.INTERNAL_SERVER_ERROR,
        message=message,
        details=details,
        request_id=request_id
    )
    return HTTPException(status_code=500, detail=error_detail)
