"""
Real SEO Configuration for Emma Studio
Configuration for authentic data sources and APIs
"""

import os
from typing import Optional

try:
    from pydantic_settings import BaseSettings
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings

class RealSEOSettings(BaseSettings):
    """Settings for Real SEO Ecommerce Service"""
    
    # Google APIs
    GOOGLE_API_KEY: str = "AIzaSyBAF5GT1Isn2TBL-s9tUsdKQDI57Y8uQ18"
    GOOGLE_CSE_ID: Optional[str] = None  # Must be configured in Google Custom Search Console
    
    # Serper API (Real Google Search Results)
    SERPER_API_KEY: str = "2187e03c0d1710eeaa3e3669daf6a4fcddc1b84cb"
    
    # BigQuery Configuration (for Google ADK)
    GOOGLE_CLOUD_PROJECT: Optional[str] = None
    BIGQUERY_DATASET: Optional[str] = None
    BIGQUERY_TABLE: Optional[str] = None
    
    # API Rate Limiting
    MAX_REQUESTS_PER_MINUTE: int = 60
    MAX_CONCURRENT_REQUESTS: int = 10
    
    # Analysis Configuration
    MAX_PRODUCTS_PER_ANALYSIS: int = 20
    MAX_KEYWORDS_PER_ANALYSIS: int = 50
    MAX_WEBSITES_PER_BRAND: int = 3
    
    # Timeout Settings
    HTTP_TIMEOUT_SECONDS: int = 15
    API_TIMEOUT_SECONDS: int = 10
    
    # Data Quality Settings
    MIN_PRODUCT_TITLE_LENGTH: int = 3
    MIN_KEYWORD_LENGTH: int = 3
    MIN_DESCRIPTION_LENGTH: int = 10
    
    # SEO Analysis Settings
    OPTIMAL_TITLE_LENGTH_MIN: int = 30
    OPTIMAL_TITLE_LENGTH_MAX: int = 60
    OPTIMAL_META_DESC_LENGTH_MIN: int = 120
    OPTIMAL_META_DESC_LENGTH_MAX: int = 160
    
    # Cache Settings
    ENABLE_CACHING: bool = True
    CACHE_TTL_SECONDS: int = 3600  # 1 hour
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields from .env

# Global settings instance
real_seo_settings = RealSEOSettings()

def get_real_seo_settings() -> RealSEOSettings:
    """Get Real SEO settings instance"""
    return real_seo_settings

def validate_api_configuration() -> dict:
    """
    Validate API configuration and return status
    """
    config_status = {
        "google_api_configured": bool(real_seo_settings.GOOGLE_API_KEY),
        "serper_api_configured": bool(real_seo_settings.SERPER_API_KEY),
        "google_cse_configured": bool(real_seo_settings.GOOGLE_CSE_ID),
        "bigquery_configured": bool(
            real_seo_settings.GOOGLE_CLOUD_PROJECT and 
            real_seo_settings.BIGQUERY_DATASET and 
            real_seo_settings.BIGQUERY_TABLE
        ),
        "configuration_complete": False
    }
    
    # Check if minimum required APIs are configured
    required_apis = [
        config_status["google_api_configured"],
        config_status["serper_api_configured"]
    ]
    
    config_status["configuration_complete"] = all(required_apis)
    
    return config_status

def get_api_usage_limits() -> dict:
    """
    Get API usage limits and recommendations
    """
    return {
        "serper_api": {
            "requests_per_minute": real_seo_settings.MAX_REQUESTS_PER_MINUTE,
            "concurrent_requests": real_seo_settings.MAX_CONCURRENT_REQUESTS,
            "recommended_usage": "Use for keyword research and search result analysis"
        },
        "google_custom_search": {
            "daily_limit": 100,  # Free tier limit
            "recommended_usage": "Use for finding brand websites and product pages"
        },
        "analysis_limits": {
            "max_products": real_seo_settings.MAX_PRODUCTS_PER_ANALYSIS,
            "max_keywords": real_seo_settings.MAX_KEYWORDS_PER_ANALYSIS,
            "max_websites": real_seo_settings.MAX_WEBSITES_PER_BRAND
        }
    }

def get_data_sources_info() -> dict:
    """
    Get information about available real data sources
    """
    return {
        "primary_sources": [
            {
                "name": "Serper API",
                "description": "Real Google search results and SERP data",
                "status": "configured" if real_seo_settings.SERPER_API_KEY else "not_configured",
                "data_types": ["search_results", "related_searches", "people_also_ask"]
            },
            {
                "name": "Google Custom Search",
                "description": "Programmable search engine for specific sites",
                "status": "configured" if real_seo_settings.GOOGLE_CSE_ID else "not_configured",
                "data_types": ["site_search", "product_discovery", "content_analysis"]
            }
        ],
        "extraction_methods": [
            {
                "name": "Intelligent Web Scraping",
                "description": "Advanced content extraction with multiple strategies",
                "capabilities": ["json_ld", "microdata", "meta_tags", "content_patterns"]
            },
            {
                "name": "Structured Data Parsing",
                "description": "Schema.org and structured data extraction",
                "capabilities": ["product_schema", "organization_schema", "breadcrumbs"]
            },
            {
                "name": "Technical SEO Analysis",
                "description": "Real-time technical SEO audit",
                "capabilities": ["meta_analysis", "heading_structure", "performance_metrics"]
            }
        ],
        "quality_assurance": [
            "No simulated or fake data",
            "Real-time API calls",
            "Authentic search results",
            "Google methodology compliance",
            "Data validation and deduplication"
        ]
    }

# Configuration validation on import
if __name__ == "__main__":
    config_status = validate_api_configuration()
    print("Real SEO Configuration Status:")
    for key, value in config_status.items():
        status = "✅" if value else "❌"
        print(f"  {status} {key}: {value}")
    
    if not config_status["configuration_complete"]:
        print("\n⚠️  Configuration incomplete. Please set the following:")
        if not config_status["google_api_configured"]:
            print("  - GOOGLE_API_KEY")
        if not config_status["serper_api_configured"]:
            print("  - SERPER_API_KEY")
        if not config_status["google_cse_configured"]:
            print("  - GOOGLE_CSE_ID (create at https://cse.google.com/cse/)")
    else:
        print("\n✅ Configuration complete - Real SEO service ready!")
