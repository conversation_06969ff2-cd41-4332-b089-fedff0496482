# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Defines tools for brand search optimization agent"""

from google.cloud import bigquery
from google.adk.tools import ToolContext

from ..shared_libraries import constants
from .web_scraper import scrape_ecommerce_site
from .google_search_scraper import search_real_products
from .intelligent_scraper import intelligent_scrape
import re
from urllib.parse import urlparse

# Sample product data for testing (expanded with real brands)
SAMPLE_PRODUCTS = [
    # BSOAgentTestBrand products
    {
        "Title": "Kids' Joggers",
        "Description": "Comfortable and supportive running shoes for active kids. Breathable mesh upper keeps feet cool, while the durable outsole provides excellent traction.",
        "Attributes": "Size: 10 Toddler, Color: Blue/Green",
        "Brand": "BSOAgentTestBrand",
    },
    {
        "Title": "Light-Up Sneakers",
        "Description": "Fun and stylish sneakers with light-up features that kids will love. Supportive and comfortable for all-day play.",
        "Attributes": "Size: 13 Toddler, Color: Silver",
        "Brand": "BSOAgentTestBrand",
    },
    {
        "Title": "School Shoes",
        "Description": "Versatile and comfortable shoes perfect for everyday wear at school. Durable construction with a supportive design.",
        "Attributes": "Size: 12 Preschool, Color: Black",
        "Brand": "BSOAgentTestBrand",
    },
    # GoPro products
    {
        "Title": "HERO12 Black Action Camera",
        "Description": "Revolutionary action camera with 5.3K video recording, HyperSmooth 6.0 stabilization, and waterproof design for extreme adventures.",
        "Attributes": "Resolution: 5.3K, Waterproof: 10m, Battery: 1720mAh",
        "Brand": "GoPro",
    },
    {
        "Title": "HERO11 Black Mini",
        "Description": "Ultra-compact action camera with 4K video, simplified controls, and rugged design perfect for mounting anywhere.",
        "Attributes": "Resolution: 4K, Weight: 133g, Mounting: Universal",
        "Brand": "GoPro",
    },
    {
        "Title": "MAX 360 Camera",
        "Description": "360-degree action camera with dual lenses, immersive capture, and advanced stabilization for creative content.",
        "Attributes": "Type: 360°, Lenses: Dual, Stabilization: Max HyperSmooth",
        "Brand": "GoPro",
    },
    # Nike products
    {
        "Title": "Air Max 270 Running Shoes",
        "Description": "Iconic running shoes with visible Air Max unit, breathable mesh upper, and comfortable foam midsole for all-day wear.",
        "Attributes": "Size: 9-12, Color: Black/White, Technology: Air Max",
        "Brand": "Nike",
    },
    {
        "Title": "Dri-FIT Training T-Shirt",
        "Description": "Moisture-wicking athletic shirt with lightweight fabric and ergonomic design for optimal performance during workouts.",
        "Attributes": "Material: Polyester, Fit: Athletic, Technology: Dri-FIT",
        "Brand": "Nike",
    },
    # Apple products
    {
        "Title": "iPhone 15 Pro Max",
        "Description": "Premium smartphone with titanium design, A17 Pro chip, advanced camera system, and all-day battery life.",
        "Attributes": "Storage: 256GB, Camera: 48MP, Display: 6.7-inch",
        "Brand": "Apple",
    },
    {
        "Title": "MacBook Air M3",
        "Description": "Ultra-thin laptop with M3 chip, 18-hour battery life, and stunning Liquid Retina display for productivity and creativity.",
        "Attributes": "Processor: M3, RAM: 16GB, Storage: 512GB SSD",
        "Brand": "Apple",
    },
]

# Initialize the BigQuery client outside the function
try:
    client = bigquery.Client()  # Initialize client once
except Exception as e:
    print(f"Error initializing BigQuery client: {e}")
    client = None  # Set client to None if initialization fails


def _is_url(text: str) -> bool:
    """
    Check if the input text is a URL

    Args:
        text: Input text to check

    Returns:
        bool: True if text is a URL, False otherwise
    """
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)

    return bool(url_pattern.match(text.strip()))


def get_product_details_for_brand(brand_name):
    """
    Retrieves product details (title, description, attributes, and brand) from sample data, BigQuery, or web scraping.

    Args:
        brand_name (str): The brand name or URL to search for.

    Returns:
        list: A list of dictionaries containing product details.
    """
    print(f"🔍 Searching for brand: '{brand_name}'")

    # Check if input is a URL
    if _is_url(brand_name):
        print(f"🌐 Detected URL, using intelligent analysis: {brand_name}")
        try:
            # First try intelligent scraping for REAL products
            intelligent_products = intelligent_scrape(brand_name)
            if intelligent_products:
                print(f"✅ Found {len(intelligent_products)} REAL products via intelligent analysis")
                return intelligent_products

            # Fallback to Google Search API
            print("🔄 Fallback to Google Search API...")
            real_products = search_real_products(brand_name)
            if real_products:
                print(f"✅ Found {len(real_products)} REAL products via Google Search API")
                return real_products

            # Final fallback to basic web scraping
            print("🔄 Final fallback to basic web scraping...")
            scraped_products = scrape_ecommerce_site(brand_name)
            if scraped_products:
                print(f"✅ Found {len(scraped_products)} products via web scraping")
                return scraped_products
            else:
                print("❌ No products found via any method")
        except Exception as e:
            print(f"❌ Analysis error: {e}")

    # First try to use sample data (fallback)
    matching_products = []

    for product in SAMPLE_PRODUCTS:
        if brand_name.lower() in product["Brand"].lower():
            matching_products.append({
                "title": product["Title"],
                "description": product["Description"],
                "category": "Electronics" if product["Brand"] in ["GoPro", "Apple"] else "Footwear" if product["Brand"] == "Nike" else "Kids Footwear",
                "price": 99.99,  # Default price
                "attributes": product["Attributes"]
            })

    if matching_products:
        print(f"✅ Found {len(matching_products)} products in sample data")
        return matching_products

    # If no sample data found, try BigQuery (if available)
    if client is not None:
        try:
            query = f"""
                SELECT
                    Title,
                    Description,
                    Attributes,
                    Brand
                FROM
                    {constants.PROJECT}.{constants.DATASET_ID}.{constants.TABLE_ID}
                WHERE brand LIKE '%{brand_name}%'
                LIMIT 10
            """

            query_job = client.query(query)
            results = query_job.result()

            bigquery_products = []
            for row in results:
                bigquery_products.append({
                    "title": row.Title,
                    "description": row.Description if row.Description else "N/A",
                    "category": "Product",
                    "price": 99.99,
                    "attributes": row.Attributes if row.Attributes else "N/A"
                })

            if bigquery_products:
                print(f"✅ Found {len(bigquery_products)} products in BigQuery")
                return bigquery_products

        except Exception as e:
            print(f"❌ BigQuery error: {e}")

    print(f"❌ No products found for brand '{brand_name}'")
    return []


# Legacy function for compatibility with ADK
def get_product_details_for_brand_legacy(tool_context):
    """Legacy function for ADK compatibility"""
    try:
        brand = tool_context.user_content.parts[0].text
    except:
        brand = str(tool_context)

    products = get_product_details_for_brand(brand)

    # Convert to markdown table format
    if not products:
        return "No products found for the specified brand."

    markdown_table = "| Title | Description | Attributes | Brand |\n"
    markdown_table += "|---|---|---|---|\n"

    for product in products[:3]:  # Limit to 3 results
        title = product["title"]
        description = product["description"]
        attributes = product["attributes"]

        markdown_table += f"| {title} | {description} | {attributes} | {brand} |\n"

    return markdown_table
