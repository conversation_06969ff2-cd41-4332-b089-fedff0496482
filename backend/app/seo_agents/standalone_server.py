#!/usr/bin/env python3
"""
Standalone SEO Agent Server
Servidor independiente para probar el SEO Agent sin afectar Emma Studio
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any
import subprocess
import sys
import os
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

app = FastAPI(
    title="Emma SEO Agent - Standalone",
    description="Servidor independiente para testing del SEO Agent",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class BrandAnalysisRequest(BaseModel):
    brand_name: str

class KeywordResult(BaseModel):
    keyword: str
    relevance_score: float = 1.0
    category: str = "product"

class ProductData(BaseModel):
    title: str
    description: str
    category: str
    price: float
    attributes: str

class BrandAnalysisResponse(BaseModel):
    brand_name: str
    total_products: int
    keywords: List[KeywordResult]
    products: List[ProductData]
    status: str
    message: str

class SEOAgentStatus(BaseModel):
    agent_name: str
    status: str
    model: str
    web_driver_enabled: bool
    bigquery_connected: bool
    server_type: str = "standalone"

def run_seo_command(command: str) -> Dict[str, Any]:
    """Run a command in the SEO agent environment"""
    try:
        full_command = f"source .venv/bin/activate && {command}"
        result = subprocess.run(
            full_command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30,
            cwd=current_dir
        )
        
        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "stdout": "",
            "stderr": "Command timed out",
            "returncode": -1
        }
    except Exception as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": str(e),
            "returncode": -1
        }

@app.get("/")
async def root():
    return {
        "message": "Emma SEO Agent - Standalone Server",
        "status": "running",
        "endpoints": {
            "status": "/status",
            "analyze": "/analyze-brand",
            "test": "/test-connection",
            "demo": "/demo"
        }
    }

@app.get("/status", response_model=SEOAgentStatus)
async def get_seo_agent_status():
    """Get the current status of the SEO agent system"""
    try:
        result = run_seo_command(
            "python -c \"from brand_search_optimization.shared_libraries import constants; "
            "print(f'{constants.AGENT_NAME}|{constants.MODEL}|{constants.DISABLE_WEB_DRIVER}')\""
        )
        
        if result["success"]:
            lines = result["stdout"].strip().split('\n')
            data_line = None
            for line in lines:
                if '|' in line and 'brand_search_optimization' in line:
                    data_line = line
                    break
            
            if data_line:
                parts = data_line.split('|')
                agent_name = parts[0]
                model = parts[1]
                web_driver_disabled = parts[2] == "1"
                
                return SEOAgentStatus(
                    agent_name=agent_name,
                    status="ready",
                    model=model,
                    web_driver_enabled=not web_driver_disabled,
                    bigquery_connected=False,
                    server_type="standalone"
                )
        
        return SEOAgentStatus(
            agent_name="brand_search_optimization",
            status="error",
            model="unknown",
            web_driver_enabled=False,
            bigquery_connected=False,
            server_type="standalone"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get SEO agent status: {str(e)}")

@app.post("/analyze-brand", response_model=BrandAnalysisResponse)
async def analyze_brand(request: BrandAnalysisRequest):
    """Analyze a brand and generate SEO keywords"""
    try:
        brand_name = request.brand_name.strip()
        
        if not brand_name:
            raise HTTPException(status_code=400, detail="Brand name is required")
        
        result = run_seo_command(
            f"python -c \"from brand_search_optimization.tools.bq_connector import get_product_details_for_brand; "
            f"import json; "
            f"products = get_product_details_for_brand('{brand_name}'); "
            f"print(json.dumps(products))\""
        )
        
        if not result["success"]:
            raise HTTPException(
                status_code=500, 
                detail=f"Failed to analyze brand: {result['stderr']}"
            )
        
        lines = result["stdout"].strip().split('\n')
        products_json = None
        
        for line in lines:
            if line.startswith('[') and line.endswith(']'):
                products_json = line
                break
        
        if not products_json:
            return BrandAnalysisResponse(
                brand_name=brand_name,
                total_products=0,
                keywords=[],
                products=[],
                status="no_data",
                message=f"No products found for brand '{brand_name}'. Try 'BSOAgentTestBrand' for demo data."
            )
        
        import json
        products_data = json.loads(products_json)
        
        products = [
            ProductData(
                title=p["title"],
                description=p["description"],
                category=p["category"],
                price=p["price"],
                attributes=p["attributes"]
            )
            for p in products_data
        ]
        
        # Generate keywords
        keywords = set()
        for product in products_data:
            title_words = product["title"].lower().split()
            keywords.update(title_words)
            
            attr_words = product["attributes"].lower().split(", ")
            keywords.update(attr_words)
            
            category_words = product["category"].lower().split()
            keywords.update(category_words)
        
        common_words = {'for', 'and', 'the', 'with', 'of', 'in', 'on', 'at', 'to', 'a', 'an', 'is', 'are'}
        filtered_keywords = [
            KeywordResult(
                keyword=k,
                relevance_score=1.0,
                category="product"
            )
            for k in keywords 
            if k not in common_words and len(k) > 2
        ]
        
        filtered_keywords.sort(key=lambda x: len(x.keyword), reverse=True)
        
        return BrandAnalysisResponse(
            brand_name=brand_name,
            total_products=len(products),
            keywords=filtered_keywords[:20],
            products=products,
            status="success",
            message=f"Successfully analyzed {len(products)} products and generated {len(filtered_keywords)} keywords"
        )
        
    except json.JSONDecodeError as e:
        raise HTTPException(status_code=500, detail=f"Failed to parse product data: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Brand analysis failed: {str(e)}")

@app.get("/test-connection")
async def test_seo_agent_connection():
    """Test if the SEO agent system is working"""
    try:
        result = run_seo_command("python test_mock_agent.py")
        
        if result["success"]:
            stdout = result["stdout"]
            if "All mock tests passed!" in stdout:
                return {
                    "status": "success",
                    "message": "SEO Agent system is fully operational",
                    "details": "All 5/5 tests passed",
                    "server_type": "standalone"
                }
            else:
                return {
                    "status": "partial",
                    "message": "SEO Agent system has some issues",
                    "details": stdout,
                    "server_type": "standalone"
                }
        else:
            return {
                "status": "error",
                "message": "SEO Agent system test failed",
                "details": result["stderr"],
                "server_type": "standalone"
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Connection test failed: {str(e)}")

@app.get("/demo")
async def get_demo_data():
    """Get demo data for testing the SEO agent"""
    return {
        "demo_brand": "BSOAgentTestBrand",
        "description": "Use this brand name to test the SEO agent with mock data",
        "expected_products": 5,
        "expected_keywords": ["athletic", "comfortable", "durable", "lightweight", "casual", "professional"],
        "test_endpoint": "/analyze-brand",
        "server_type": "standalone",
        "note": "This is a standalone server for testing only"
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Emma SEO Agent - Standalone Server")
    print("📍 Server will run on: http://localhost:8002")
    print("🔒 This server is for testing only - not accessible to Emma Studio users")
    
    uvicorn.run(
        "standalone_server:app",
        host="127.0.0.1",
        port=8002,
        reload=True,
        log_level="info"
    )
