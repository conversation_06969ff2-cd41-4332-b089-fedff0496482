#!/usr/bin/env python3
"""
Test script for URL scraping functionality in Emma Studio SEO Agent
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from brand_search_optimization.tools.bq_connector import get_product_details_for_brand
from brand_search_optimization.tools.web_scraper import scrape_ecommerce_site

def test_url_detection():
    """Test URL detection and scraping"""
    print("🚀 Emma Studio SEO Agent - URL Scraping Test")
    print("=" * 60)
    
    # Test URLs
    test_urls = [
        "https://www.cuidadoconelperro.com.mx/",
        "https://www.amazon.com.mx/",
        "https://www.mercadolibre.com.mx/"
    ]
    
    # Test brand names (should use sample data)
    test_brands = [
        "Nike",
        "Apple",
        "GoPro"
    ]
    
    print("\n📋 Testing URL Scraping...")
    print("-" * 40)
    
    for url in test_urls:
        print(f"\n🌐 Testing URL: {url}")
        try:
            products = get_product_details_for_brand(url)
            if products:
                print(f"✅ Found {len(products)} products:")
                for i, product in enumerate(products[:2], 1):
                    print(f"   {i}. {product['title']} - ${product['price']}")
            else:
                print("❌ No products found")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n📋 Testing Brand Names (Sample Data)...")
    print("-" * 40)
    
    for brand in test_brands:
        print(f"\n🏷️ Testing Brand: {brand}")
        try:
            products = get_product_details_for_brand(brand)
            if products:
                print(f"✅ Found {len(products)} products:")
                for i, product in enumerate(products[:2], 1):
                    print(f"   {i}. {product['title']} - ${product['price']}")
            else:
                print("❌ No products found")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n🎯 Testing Direct Web Scraper...")
    print("-" * 40)
    
    test_url = "https://www.cuidadoconelperro.com.mx/"
    print(f"\n🔍 Direct scraping: {test_url}")
    try:
        products = scrape_ecommerce_site(test_url)
        if products:
            print(f"✅ Scraped {len(products)} products:")
            for i, product in enumerate(products, 1):
                print(f"   {i}. {product['title']}")
                print(f"      Category: {product['category']}")
                print(f"      Price: ${product['price']}")
                print(f"      Description: {product['description'][:80]}...")
                print()
        else:
            print("❌ No products scraped")
    except Exception as e:
        print(f"❌ Scraping error: {e}")
    
    print("\n🎉 URL Scraping Test Complete!")
    print("=" * 60)

if __name__ == "__main__":
    test_url_detection()
