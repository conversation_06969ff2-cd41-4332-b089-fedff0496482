# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from google.cloud import bigquery

from brand_search_optimization.shared_libraries import constants

PROJECT = constants.PROJECT
TABLE_ID = constants.TABLE_ID
LOCATION = constants.LOCATION
DATASET_ID = constants.DATASET_ID
TABLE_ID = constants.TABLE_ID

client = bigquery.Client(project=PROJECT)

# Sample data to insert
data_to_insert = [
    # BSOAgentTestBrand products
    {
        "Title": "Kids' Joggers",
        "Description": "Comfortable and supportive running shoes for active kids. Breathable mesh upper keeps feet cool, while the durable outsole provides excellent traction.",
        "Attributes": "Size: 10 Toddler, Color: Blue/Green",
        "Brand": "BSOAgentTestBrand",
    },
    {
        "Title": "Light-Up Sneakers",
        "Description": "Fun and stylish sneakers with light-up features that kids will love. Supportive and comfortable for all-day play.",
        "Attributes": "Size: 13 Toddler, Color: Silver",
        "Brand": "BSOAgentTestBrand",
    },
    {
        "Title": "School Shoes",
        "Description": "Versatile and comfortable shoes perfect for everyday wear at school. Durable construction with a supportive design.",
        "Attributes": "Size: 12 Preschool, Color: Black",
        "Brand": "BSOAgentTestBrand",
    },
    # GoPro products
    {
        "Title": "HERO12 Black Action Camera",
        "Description": "Revolutionary action camera with 5.3K video recording, HyperSmooth 6.0 stabilization, and waterproof design for extreme adventures.",
        "Attributes": "Resolution: 5.3K, Waterproof: 10m, Battery: 1720mAh",
        "Brand": "GoPro",
    },
    {
        "Title": "HERO11 Black Mini",
        "Description": "Ultra-compact action camera with 4K video, simplified controls, and rugged design perfect for mounting anywhere.",
        "Attributes": "Resolution: 4K, Weight: 133g, Mounting: Universal",
        "Brand": "GoPro",
    },
    {
        "Title": "MAX 360 Camera",
        "Description": "360-degree action camera with dual lenses, immersive capture, and advanced stabilization for creative content.",
        "Attributes": "Type: 360°, Lenses: Dual, Stabilization: Max HyperSmooth",
        "Brand": "GoPro",
    },
    # Nike products
    {
        "Title": "Air Max 270 Running Shoes",
        "Description": "Iconic running shoes with visible Air Max unit, breathable mesh upper, and comfortable foam midsole for all-day wear.",
        "Attributes": "Size: 9-12, Color: Black/White, Technology: Air Max",
        "Brand": "Nike",
    },
    {
        "Title": "Dri-FIT Training T-Shirt",
        "Description": "Moisture-wicking athletic shirt with lightweight fabric and ergonomic design for optimal performance during workouts.",
        "Attributes": "Material: Polyester, Fit: Athletic, Technology: Dri-FIT",
        "Brand": "Nike",
    },
    # Apple products
    {
        "Title": "iPhone 15 Pro Max",
        "Description": "Premium smartphone with titanium design, A17 Pro chip, advanced camera system, and all-day battery life.",
        "Attributes": "Storage: 256GB, Camera: 48MP, Display: 6.7-inch",
        "Brand": "Apple",
    },
    {
        "Title": "MacBook Air M3",
        "Description": "Ultra-thin laptop with M3 chip, 18-hour battery life, and stunning Liquid Retina display for productivity and creativity.",
        "Attributes": "Processor: M3, RAM: 16GB, Storage: 512GB SSD",
        "Brand": "Apple",
    },
]


def create_dataset_if_not_exists():
    """Creates a BigQuery dataset if it does not already exist."""
    # Construct a BigQuery client object.
    dataset_id = f"{client.project}.{DATASET_ID}"
    dataset = bigquery.Dataset(dataset_id)
    dataset.location = "US"
    client.delete_dataset(
        dataset_id, delete_contents=True, not_found_ok=True
    )  # Make an API request.
    dataset = client.create_dataset(dataset)  # Make an API request.
    print("Created dataset {}.{}".format(client.project, dataset.dataset_id))
    return dataset


def populate_bigquery_table():
    """Populates a BigQuery table with the provided data."""
    dataset_ref = create_dataset_if_not_exists()
    if not dataset_ref:
        return

    # Define the schema based on your CREATE TABLE statement
    schema = [
        bigquery.SchemaField("Title", "STRING"),
        bigquery.SchemaField("Description", "STRING"),
        bigquery.SchemaField("Attributes", "STRING"),
        bigquery.SchemaField("Brand", "STRING"),
    ]
    table_id = f"{PROJECT}.{DATASET_ID}.{TABLE_ID}"
    table = bigquery.Table(table_id, schema=schema)
    client.delete_table(table_id, not_found_ok=True)  # Make an API request.
    print("Deleted table '{}'.".format(table_id))
    table = client.create_table(table)  # Make an API request.
    print(
        "Created table {}.{}.{}".format(
            PROJECT, table.dataset_id, table.table_id
        )
    )

    errors = client.insert_rows_json(table=table, json_rows=data_to_insert)

    if errors == []:
        print(
            f"Successfully inserted {len(data_to_insert)} rows into {PROJECT}.{DATASET_ID}.{TABLE_ID}"
        )
    else:
        print("Errors occurred while inserting rows:")
        for error in errors:
            print(error)


if __name__ == "__main__":
    populate_bigquery_table()
    print(
        "\n--- Instructions on how to add permissions to BQ Table are in the customiztion.md file ---"
    )
